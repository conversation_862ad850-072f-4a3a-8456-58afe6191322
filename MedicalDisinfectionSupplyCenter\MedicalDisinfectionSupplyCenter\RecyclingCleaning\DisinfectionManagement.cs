﻿using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WinFormsAppDemo2.Common;

namespace MedicalDisinfectionSupplyCenter.RecyclingCleaning
{
    public partial class DisinfectionManagement : UserControl
    {
        private string ApiBaseUrl = ApiUrl.ApiReadUrl; // 读取API地址
        private string ApiWriteUrl = ApiUrl.ApiWriteUrl; // 写入API地址

        public DisinfectionManagement()
        {
            InitializeComponent();
            InitializeGridControl();
        }

        private void InitializeGridControl()
        {
            // 初始化gridView1 (已完成列表)
            InitializeGridView1();

            // 初始化gridView2 (消毒列表)
            InitializeGridView2();
        }

        private void InitializeGridView1()
        {
            // 设置GridView1属性 (已完成列表)
            gridView1.OptionsBehavior.Editable = false;
            gridView1.OptionsView.ShowGroupPanel = false;
            gridView1.OptionsView.ShowIndicator = true;
            gridView1.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;

            // 设置列
            gridView1.Columns.Clear();
            gridView1.Columns.AddVisible("DisinfectionBatch", "消毒批次");
            gridView1.Columns.AddVisible("Disinfector", "消毒人");
            gridView1.Columns.AddVisible("FinishTime", "完成时间");
            gridView1.Columns.AddVisible("Operation", "操作");

            // 设置列宽
            gridView1.Columns["DisinfectionBatch"].Width = 120;
            gridView1.Columns["Disinfector"].Width = 100;
            gridView1.Columns["FinishTime"].Width = 150;
            gridView1.Columns["Operation"].Width = 100;

            // 格式化日期列
            gridView1.Columns["FinishTime"].DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            gridView1.Columns["FinishTime"].DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";

            // 设置行高
            gridView1.RowHeight = 35;

            // 绑定事件
            gridView1.CustomColumnDisplayText += GridView1_CustomColumnDisplayText;
            gridView1.RowClick += GridView1_RowClick;
            gridView1.CustomDrawCell += GridView1_CustomDrawCell;
            gridView1.MouseMove += GridView1_MouseMove;
        }

        private void InitializeGridView2()
        {
            // 设置GridView2属性 (消毒列表)
            gridView2.OptionsBehavior.Editable = false;
            gridView2.OptionsView.ShowGroupPanel = false;
            gridView2.OptionsView.ShowIndicator = true;
            gridView2.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
            gridView2.OptionsView.ShowColumnHeaders = true;
            gridView2.OptionsView.EnableAppearanceEvenRow = true;
            gridView2.OptionsView.EnableAppearanceOddRow = true;

            // 设置选择模式
            gridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            gridView2.OptionsSelection.EnableAppearanceFocusedRow = true;
            gridView2.OptionsSelection.MultiSelect = false;

            // 设置列
            gridView2.Columns.Clear();
            var colId = gridView2.Columns.AddField("Id"); // 添加隐藏的ID列
            colId.Visible = false; // 设置为不可见
            var colDisinfectionBatch = gridView2.Columns.AddVisible("DisinfectionBatch", "消毒批次");
            var colStartTime = gridView2.Columns.AddVisible("StartTime", "开始时间");
            var colDisinfectionDuration = gridView2.Columns.AddVisible("DisinfectionDuration", "消毒时长");
            var colDisinfector = gridView2.Columns.AddVisible("Disinfector", "消毒人");
            var colOperation = gridView2.Columns.AddVisible("Operation", "操作");

            // 设置列宽和对齐方式
            colDisinfectionBatch.Width = 120;
            colDisinfectionBatch.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            colDisinfectionBatch.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;

            colStartTime.Width = 150;
            colStartTime.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            colStartTime.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;

            colDisinfectionDuration.Width = 100;
            colDisinfectionDuration.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            colDisinfectionDuration.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;

            colDisinfector.Width = 100;
            colDisinfector.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            colDisinfector.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;

            colOperation.Width = 150;
            colOperation.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            colOperation.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;

            // 格式化日期列
            colStartTime.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            colStartTime.DisplayFormat.FormatString = "MM-dd HH:mm";

            // 设置行高
            gridView2.RowHeight = 40;

            // 设置外观样式
            SetGridView2Appearance();

            // 绑定事件
            gridView2.CustomColumnDisplayText += GridView2_CustomColumnDisplayText;
            gridView2.RowClick += GridView2_RowClick;
            gridView2.CustomDrawCell += GridView2_CustomDrawCell;
            gridView2.MouseMove += GridView2_MouseMove;
        }

        private void SetGridView2Appearance()
        {
            // 设置表格外观
            gridView2.Appearance.HeaderPanel.BackColor = Color.FromArgb(240, 248, 255);
            gridView2.Appearance.HeaderPanel.ForeColor = Color.FromArgb(64, 64, 64);
            gridView2.Appearance.HeaderPanel.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            gridView2.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;

            gridView2.Appearance.Row.BackColor = Color.White;
            gridView2.Appearance.Row.ForeColor = Color.FromArgb(64, 64, 64);
            gridView2.Appearance.Row.Font = new Font("微软雅黑", 9F);

            gridView2.Appearance.EvenRow.BackColor = Color.FromArgb(248, 250, 252);
            gridView2.Appearance.OddRow.BackColor = Color.White;

            gridView2.Appearance.FocusedRow.BackColor = Color.FromArgb(220, 237, 255);
            gridView2.Appearance.FocusedRow.ForeColor = Color.FromArgb(64, 64, 64);

            gridView2.Appearance.SelectedRow.BackColor = Color.FromArgb(184, 218, 255);
            gridView2.Appearance.SelectedRow.ForeColor = Color.FromArgb(64, 64, 64);
        }

        private void DisinfectionManagement_Load(object sender, EventArgs e)
        {
            // 设置默认日期
            dateEdit1.EditValue = DateTime.Today;

            // 加载数据
            LoadDisinfectionListData();
            LoadCompletedDisinfectionData();
        }

        private async void LoadDisinfectionListData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 开始加载消毒列表数据");

                // 创建模拟数据
                var disinfectionData = new List<object>
                {
                    new { Id = 1, DisinfectionBatch = "XD20241201001", StartTime = DateTime.Now.AddHours(-2), DisinfectionDuration = "2小时", Disinfector = "张三", Operation = "操作" },
                    new { Id = 2, DisinfectionBatch = "XD20241201002", StartTime = DateTime.Now.AddHours(-1), DisinfectionDuration = "1.5小时", Disinfector = "李四", Operation = "操作" },
                    new { Id = 3, DisinfectionBatch = "XD20241201003", StartTime = DateTime.Now.AddMinutes(-30), DisinfectionDuration = "0.5小时", Disinfector = "王五", Operation = "操作" }
                };

                gridControl2.DataSource = disinfectionData;
                System.Diagnostics.Debug.WriteLine($"✅ 消毒列表数据加载完成，共 {disinfectionData.Count} 条记录");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 加载消毒列表数据失败: {ex.Message}");
                MessageBox.Show($"加载消毒列表数据失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void LoadCompletedDisinfectionData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 开始加载已完成消毒数据");

                // 创建模拟数据
                var completedData = new List<object>
                {
                    new { DisinfectionBatch = "XD20241130001", Disinfector = "赵六", FinishTime = DateTime.Now.AddDays(-1), Operation = "操作" },
                    new { DisinfectionBatch = "XD20241130002", Disinfector = "钱七", FinishTime = DateTime.Now.AddDays(-1).AddHours(-2), Operation = "操作" }
                };

                gridControl1.DataSource = completedData;
                System.Diagnostics.Debug.WriteLine($"✅ 已完成消毒数据加载完成，共 {completedData.Count} 条记录");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 加载已完成消毒数据失败: {ex.Message}");
                MessageBox.Show($"加载已完成消毒数据失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // GridView2 事件处理 (消毒列表)
        private void GridView2_CustomColumnDisplayText(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventArgs e)
        {
            if (e.Column.FieldName == "Operation")
            {
                e.DisplayText = "明细 | 删除 | 完成";
            }
        }

        private void GridView2_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            var gridView = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            var hitInfo = gridView.CalcHitInfo(new Point(e.X, e.Y));

            if (hitInfo.InRowCell && hitInfo.Column != null && hitInfo.Column.FieldName == "Operation")
            {
                // 获取当前行的ID和消毒批次
                var disinfectionId = gridView.GetRowCellValue(e.RowHandle, "Id")?.ToString() ?? "0";
                var disinfectionBatch = gridView.GetRowCellValue(e.RowHandle, "DisinfectionBatch")?.ToString() ?? "未知";

                System.Diagnostics.Debug.WriteLine($"🔍 消毒列表点击操作: ID={disinfectionId}, Batch={disinfectionBatch}");

                // 显示操作选择对话框
                ShowDisinfectionOperationDialog(disinfectionId, disinfectionBatch);
            }
        }

        private void GridView2_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            if (e.Column.FieldName == "Operation")
            {
                e.Appearance.ForeColor = Color.FromArgb(0, 123, 255);
                e.Appearance.Font = new Font(e.Appearance.Font, FontStyle.Underline);
            }
        }

        private void GridView2_MouseMove(object sender, MouseEventArgs e)
        {
            var gridView = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            var hitInfo = gridView.CalcHitInfo(e.Location);

            if (hitInfo.InRowCell && hitInfo.Column != null && hitInfo.Column.FieldName == "Operation")
            {
                gridView.GridControl.Cursor = Cursors.Hand;
            }
            else
            {
                gridView.GridControl.Cursor = Cursors.Default;
            }
        }

        // GridView1 事件处理 (已完成列表)
        private void GridView1_CustomColumnDisplayText(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventArgs e)
        {
            if (e.Column.FieldName == "Operation")
            {
                e.DisplayText = "查看 | 撤销";
            }
        }

        private void GridView1_RowClick(object sender, DevExpress.XtraGrid.Views.Grid.RowClickEventArgs e)
        {
            var gridView = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            var hitInfo = gridView.CalcHitInfo(new Point(e.X, e.Y));

            if (hitInfo.InRowCell && hitInfo.Column != null && hitInfo.Column.FieldName == "Operation")
            {
                // 获取当前行的消毒批次
                var disinfectionBatch = gridView.GetRowCellValue(e.RowHandle, "DisinfectionBatch")?.ToString() ?? "未知";

                System.Diagnostics.Debug.WriteLine($"🔍 已完成列表点击操作: Batch={disinfectionBatch}");

                // 显示操作选择对话框
                ShowCompletedDisinfectionOperationDialog("0", disinfectionBatch);
            }
        }

        private void GridView1_CustomDrawCell(object sender, DevExpress.XtraGrid.Views.Base.RowCellCustomDrawEventArgs e)
        {
            if (e.Column.FieldName == "Operation")
            {
                e.Appearance.ForeColor = Color.FromArgb(0, 123, 255);
                e.Appearance.Font = new Font(e.Appearance.Font, FontStyle.Underline);
            }
        }

        private void GridView1_MouseMove(object sender, MouseEventArgs e)
        {
            var gridView = sender as DevExpress.XtraGrid.Views.Grid.GridView;
            var hitInfo = gridView.CalcHitInfo(e.Location);

            if (hitInfo.InRowCell && hitInfo.Column != null && hitInfo.Column.FieldName == "Operation")
            {
                gridView.GridControl.Cursor = Cursors.Hand;
            }
            else
            {
                gridView.GridControl.Cursor = Cursors.Default;
            }
        }

        // 操作对话框方法
        private void ShowCompletedDisinfectionOperationDialog(string disinfectionId, string disinfectionBatch)
        {
            // 创建已完成消毒的操作选择对话框
            using (var dialog = new CompletedDisinfectionOperationDialog(disinfectionBatch))
            {
                var result = dialog.ShowDialog(this);

                if (result == DialogResult.OK)
                {
                    switch (dialog.Result)
                    {
                        case CompletedDisinfectionOperationDialog.OperationResult.View:
                            ShowDisinfectionDetails(disinfectionBatch);
                            break;
                        case CompletedDisinfectionOperationDialog.OperationResult.Cancel_Operation:
                            CancelDisinfectionState(disinfectionId, disinfectionBatch);
                            break;
                    }
                }
            }
        }

        private void ShowDisinfectionOperationDialog(string disinfectionId, string disinfectionBatch)
        {
            // 使用自定义对话框
            using (var operationDialog = new DisinfectionOperationDialog(disinfectionBatch))
            {
                if (operationDialog.ShowDialog() == DialogResult.OK)
                {
                    switch (operationDialog.Result)
                    {
                        case DisinfectionOperationDialog.OperationResult.Details:
                            ShowDisinfectionDetails(disinfectionBatch);
                            break;
                        case DisinfectionOperationDialog.OperationResult.Delete:
                            DeleteDisinfection(disinfectionBatch);
                            break;
                        case DisinfectionOperationDialog.OperationResult.Complete:
                            CompleteDisinfection(disinfectionId, disinfectionBatch);
                            break;
                    }
                }
                // 如果用户按ESC或关闭对话框，不执行任何操作
            }
        }

        private void ShowDisinfectionDetails(string disinfectionId)
        {
            // 这里实现显示消毒详情的逻辑
            MessageBox.Show($"显示消毒ID为 {disinfectionId} 的详情", "消毒详情", MessageBoxButtons.OK, MessageBoxIcon.Information);
            // 可以打开一个详情对话框，并提供标记完成的功能
        }

        private void DeleteDisinfection(string disinfectionId)
        {
            // 确认是否删除
            DialogResult result = MessageBox.Show($"确定要删除ID为 {disinfectionId} 的消毒记录吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                // 实现删除逻辑
                MessageBox.Show($"已删除ID为 {disinfectionId} 的消毒记录", "删除成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                // 删除后刷新数据
                LoadDisinfectionListData();
            }
        }

        /// <summary>
        /// 完成消毒的修改状态的方法
        /// </summary>
        /// <param name="disinfectionId">消毒编号Id</param>
        /// <param name="disinfectionBatch">消毒批次</param>
        private async void CompleteDisinfection(string disinfectionId, string disinfectionBatch)
        {
            try
            {
                // 打开完成消毒对话框
                using (var completionForm = new DisinfectionCompletionForm(disinfectionBatch))
                {
                    if (completionForm.ShowDialog() == DialogResult.OK)
                    {
                        // 获取用户输入的信息
                        var finishPerson = completionForm.FinishPerson;
                        var finishTime = completionForm.FinishTime;
                        var disinfectionResult = completionForm.DisinfectionResult;

                        // 调用API更新消毒完成状态，使用disinfectionId
                        bool success = await UpdateDisinfectionCompletedAsync(disinfectionId, finishTime, disinfectionResult, finishPerson);

                        if (success)
                        {
                            MessageBox.Show($"消毒批次 {disinfectionBatch} 已标记为完成", "操作成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            // 刷新数据
                            LoadDisinfectionListData();
                            LoadCompletedDisinfectionData();
                        }
                        else
                        {
                            MessageBox.Show("更新消毒状态失败，请重试", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void 消毒管理_Click(object sender, EventArgs e)
        {
            // 刷新数据
            System.Diagnostics.Debug.WriteLine("手动刷新数据开始");
            LoadDisinfectionListData();
            LoadCompletedDisinfectionData();
            System.Diagnostics.Debug.WriteLine("手动刷新数据完成");
        }

        // 添加消毒登记功能
        private void button1_Click(object sender, EventArgs e)
        {
            // 打开消毒登记对话框
            OpenDisinfectionRegistrationDialog();
        }

        private async void OpenDisinfectionRegistrationDialog()
        {
            try
            {
                using (var registrationForm = new DisinfectionRegistrationForm())
                {
                    // 可选：测试API连接
                    bool apiConnected = await registrationForm.TestApiConnectionAsync();
                    if (!apiConnected)
                    {
                        var result = MessageBox.Show(
                            "无法连接到API服务器，将使用示例数据。\n\n是否继续？",
                            "API连接警告",
                            MessageBoxButtons.YesNo,
                            MessageBoxIcon.Warning);

                        if (result == DialogResult.No)
                        {
                            return;
                        }
                    }

                    var dialogResult = registrationForm.ShowDialog();
                    if (dialogResult == DialogResult.OK)
                    {
                        // 获取登记信息
                        var disinfector = registrationForm.Disinfector;
                        var startTime = registrationForm.StartTime;
                        var method = registrationForm.DisinfectionMethod;
                        var equipment = registrationForm.DisinfectionEquipment;
                        var temperature = registrationForm.DisinfectionTemperature;
                        var selectedItems = registrationForm.SelectedItems;

                        // 显示登记成功信息
                        string itemNames = string.Join(", ", selectedItems.ConvertAll(item => item.ItemName));
                        string message = $"消毒登记成功！\n\n消毒人员：{disinfector}\n消毒方式：{method}\n消毒设备：{equipment}\n消毒温度：{temperature}\n选中物品：{itemNames}";

                        MessageBox.Show(message, "登记成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // 刷新消毒列表数据
                        LoadDisinfectionListData();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开消毒登记窗口失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 撤销消毒状态的方法
        /// </summary>
        /// <param name="disinfectionId">消毒编号Id</param>
        /// <param name="disinfectionBatch">消毒批次</param>
        private async void CancelDisinfectionState(string disinfectionId, string disinfectionBatch)
        {
            try
            {
                // 确认撤销操作
                DialogResult confirmResult = MessageBox.Show(
                    $"确定要撤销消毒批次 {disinfectionBatch} 的完成状态吗？\n撤销后该记录将重新回到消毒列表中。",
                    "确认撤销",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (confirmResult == DialogResult.Yes)
                {
                    // 调用API撤销消毒完成状态
                    bool success = await CancelDisinfectionCompletedAsync(disinfectionId);

                    if (success)
                    {
                        MessageBox.Show($"消毒批次 {disinfectionBatch} 的完成状态已撤销", "撤销成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // 刷新数据
                        LoadDisinfectionListData();
                        LoadCompletedDisinfectionData();
                    }
                    else
                    {
                        MessageBox.Show("撤销消毒状态失败，请重试", "撤销失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"撤销操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 调用API更新消毒完成状态
        /// </summary>
        private async Task<bool> UpdateDisinfectionCompletedAsync(string disinfectionId, DateTime finishTime, string disinfectionResult, string finishPerson)
        {
            try
            {
                // 这里应该调用实际的API
                // 暂时返回true模拟成功
                await Task.Delay(500); // 模拟API调用延迟
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 更新消毒完成状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 调用API撤销消毒完成状态
        /// </summary>
        private async Task<bool> CancelDisinfectionCompletedAsync(string disinfectionId)
        {
            try
            {
                // 这里应该调用实际的API
                // 暂时返回true模拟成功
                await Task.Delay(500); // 模拟API调用延迟
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 撤销消毒完成状态失败: {ex.Message}");
                return false;
            }
        }

        private void dateEdit1_EditValueChanged(object sender, EventArgs e)
        {

        }
    }
}
