using DevExpress.XtraBars;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Net.Http;
using System.Threading.Tasks;
using System.Reflection;
using Newtonsoft.Json;

namespace MedicalDisinfectionSupplyCenter
{
    public partial class FluentDesignForm1 : DevExpress.XtraBars.FluentDesignSystem.FluentDesignForm
    {
        private string _userName;
        private string _roleName;
        private string _permissionsJson;
        private List<PermissionDto> _permissions;
        private List<PermissionDto> _topMenus;
        private int _currentTopMenuId = 0;
        private Dictionary<string, UserControl> _pageCache = new Dictionary<string, UserControl>();

        // 添加API基础地址常量
        //private const string API_BASE_URL = "http://localhost:5172/api/RBAC/";
        private const string API_BASE_URL = "http://***********:4050/api/RBAC/";
        // 添加调试模式标志
        private bool _debugMode = false; // 改为false关闭调试消息框

        public FluentDesignForm1(string userName, string roleName, string permissionsJson)
        {
            InitializeComponent();
            _userName = userName;
            _roleName = roleName;
            _permissionsJson = permissionsJson;
            InitUserInfo();
            // 异步加载权限和菜单
            this.Load += FluentDesignForm1_Load;
        }

        private void InitUserInfo()
        {
            // 右上角显示用户名和角色，并添加退出按钮
            var userInfoItem = new DevExpress.XtraBars.BarStaticItem();
            userInfoItem.Caption = $"{_userName} | {_roleName}";
            userInfoItem.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            fluentDesignFormControl1.Items.Add(userInfoItem);
            fluentDesignFormControl1.TitleItemLinks.Add(userInfoItem);

            var logoutBtn = new DevExpress.XtraBars.BarButtonItem();
            logoutBtn.Caption = "退出登录";
            logoutBtn.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            logoutBtn.ItemClick += (s, e) =>
            {
                this.Hide();
                var loginForm = new Login();
                loginForm.Show();
                this.Close();
            };
            fluentDesignFormControl1.Items.Add(logoutBtn);
            fluentDesignFormControl1.TitleItemLinks.Add(logoutBtn);
        }

        private async void FluentDesignForm1_Load(object sender, EventArgs e)
        {
            try
            {
                // 确保控件可见
                EnsureAccordionControlVisible();

                // 从API获取顶层菜单数据
                _topMenus = await GetTopMenusAsync();
                if (_topMenus == null || !_topMenus.Any())
                {
                    System.Diagnostics.Debug.WriteLine("顶层菜单数据获取失败或为空，创建测试菜单");
                    _topMenus = CreateTestMenus();
                }

                // 初始化顶部菜单
                InitTopMenu();

                // 默认选中第一个顶部菜单
                if (_topMenus.Any())
                {
                    _currentTopMenuId = _topMenus[0].GetId();

                    // 高亮显示选中的顶部菜单
                    HighlightTopMenuItem(_currentTopMenuId);

                    // 加载左侧菜单 - 根据选中的顶部菜单ID
                    await LoadLeftMenuAsync(_currentTopMenuId);

                    // 显示欢迎页
                    ShowWelcome();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 确保左侧菜单控件可见
        private void EnsureAccordionControlVisible()
        {
            if (this.InvokeRequired)
            {
                this.Invoke((MethodInvoker)delegate
                {
                    EnsureAccordionControlVisible();
                });
                return;
            }

            if (accordionControl1 == null)
            {
                System.Diagnostics.Debug.WriteLine("错误: accordionControl1为空!");
                return;
            }

            // 确保控件可见
            accordionControl1.Visible = true;
            accordionControl1.Dock = DockStyle.Left;

            // 确保控件有合理的宽度
            if (accordionControl1.Width < 200)
            {
                accordionControl1.Width = 200;
            }

            // 确保显示模式正确
            accordionControl1.OptionsMinimizing.State = DevExpress.XtraBars.Navigation.AccordionControlState.Normal;

            // 其他设置
            accordionControl1.AllowItemSelection = true;
            accordionControl1.ShowFilterControl = DevExpress.XtraBars.Navigation.ShowFilterControl.Auto;

            // 刷新控件
            accordionControl1.Refresh();
        }

        // 获取顶层菜单数据 (parentId = 0)
        private async Task<List<PermissionDto>> GetTopMenusAsync()
        {
            try
            {
                using (var client = new HttpClient())
                {
                    // 使用GET请求替代POST请求，将参数添加到URL中
                    string url = $"{API_BASE_URL}Handle?parentId=0";
                    System.Diagnostics.Debug.WriteLine($"请求顶部菜单URL: {url}");

                    var response = await client.GetStringAsync(url);

                    // 记录原始响应内容
                    System.Diagnostics.Debug.WriteLine($"API原始响应: {response}");

                    try
                    {
                        // 尝试直接解析为权限列表（如果API直接返回数组）
                        var directList = JsonConvert.DeserializeObject<List<PermissionDto>>(response);
                        if (directList != null && directList.Any())
                        {
                            System.Diagnostics.Debug.WriteLine($"直接解析为列表成功: {directList.Count} 项");
                            return directList;
                        }
                    }
                    catch
                    {
                        // 忽略这个错误，继续尝试解析为包装对象
                        System.Diagnostics.Debug.WriteLine("尝试直接解析为列表失败，尝试解析为包装对象");
                    }

                    // 解析API响应包装对象
                    var apiResponse = JsonConvert.DeserializeObject<ApiResponse<List<PermissionDto>>>(response);

                    // 检查API调用是否成功
                    if (apiResponse != null && (apiResponse.GetCode() == 200 || apiResponse.GetData() != null))
                    {
                        var result = apiResponse.GetData() ?? new List<PermissionDto>();
                        System.Diagnostics.Debug.WriteLine($"成功获取到 {result.Count} 个顶部菜单项");
                        return result;
                    }
                    else
                    {
                        string errorMsg = apiResponse != null ? apiResponse.GetMsg() : "未知错误";
                        System.Diagnostics.Debug.WriteLine($"API返回错误: {errorMsg}");
                        MessageBox.Show($"API返回错误: {errorMsg}", "获取顶层菜单失败", MessageBoxButtons.OK, MessageBoxIcon.Error);

                        // 创建一些测试菜单以便调试
                        return CreateTestMenus();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取顶层菜单异常: {ex.Message}");
                MessageBox.Show("获取顶层菜单失败: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // 创建一些测试菜单以便调试
                return CreateTestMenus();
            }
        }

        // 创建测试菜单项，用于API不可用时的调试
        private List<PermissionDto> CreateTestMenus()
        {
            System.Diagnostics.Debug.WriteLine("创建测试菜单用于调试");
            var testMenus = new List<PermissionDto>
            {
                new PermissionDto { permissionId = 1, parentId = 0, permissionName = "系统管理", permissionUrl = "" },
                new PermissionDto { permissionId = 2, parentId = 0, permissionName = "报表管理", permissionUrl = "" },
                new PermissionDto { permissionId = 3, parentId = 0, permissionName = "设备管理", permissionUrl = "" },
                new PermissionDto { permissionId = 4, parentId = 0, permissionName = "基础数据", permissionUrl = "" },
                new PermissionDto { permissionId = 5, parentId = 0, permissionName = "工作台", permissionUrl = "Workbenches.Workbenches" }
            };
            return testMenus;
        }

        // 新增方法，专门处理顶部菜单点击
        private void HandleTopMenuClick(int parentId)
        {
            try
            {
                _currentTopMenuId = parentId;
                var topMenu = _topMenus.FirstOrDefault(m => m.GetId() == parentId);
                if (topMenu != null && (topMenu.GetName() == "工作台" || topMenu.GetUrl() == "Workbenches.Workbenches"))
                {
                    NavigateToPage("工作台", "Workbenches.Workbenches");
                    return;
                }

                // 输出详细的调试信息
                System.Diagnostics.Debug.WriteLine($"处理顶部菜单点击，ID: {parentId}");

                // 确保在UI线程上执行UI相关操作
                if (this.InvokeRequired)
                {
                    this.Invoke((MethodInvoker)delegate
                    {
                        // 高亮显示选中的顶部菜单
                        HighlightTopMenuItem(parentId);

                        // 显示加载指示器
                        ShowLoadingIndicator(true);

                        // 显示加载状态
                        ShowStatusMessage($"正在加载 ID={parentId} 的子菜单...");
                    });
                }
                else
                {
                    // 高亮显示选中的顶部菜单
                    HighlightTopMenuItem(parentId);

                    // 显示加载指示器
                    ShowLoadingIndicator(true);

                    // 显示加载状态
                    ShowStatusMessage($"正在加载 ID={parentId} 的子菜单...");
                }

                // 异步加载左侧菜单 - 避免UI阻塞
                Task.Run(async () =>
                {
                    try
                    {
                        // 调用API获取子菜单数据并加载左侧菜单
                        System.Diagnostics.Debug.WriteLine($"正在调用API获取ID为 {parentId} 的子菜单");

                        // 加载左侧菜单
                        await LoadLeftMenuAsync(parentId);

                        // 获取当前选中的顶部菜单项
                        var currentTopMenu = _topMenus.FirstOrDefault(m => m.GetId() == parentId);

                        // 在UI线程上执行UI更新操作
                        this.Invoke((MethodInvoker)delegate
                        {
                            // 隐藏加载指示器
                            ShowLoadingIndicator(false);

                            // 清除状态信息
                            ShowStatusMessage("");

                            // 如果顶部菜单有指定的页面URL，则导航到该页面，否则显示欢迎页
                            if (currentTopMenu != null)
                            {
                                if (!string.IsNullOrEmpty(currentTopMenu.GetUrl()))
                                {
                                    NavigateToPage(currentTopMenu.GetName(), currentTopMenu.GetUrl());
                                }
                                else
                                {
                                    ShowWelcome(currentTopMenu.GetName());
                                }
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        // 在UI线程上显示错误信息
                        this.Invoke((MethodInvoker)delegate
                        {
                            ShowLoadingIndicator(false);
                            ShowStatusMessage($"加载失败: {ex.Message}");
                            MessageBox.Show($"加载左侧菜单失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        });
                    }
                });
            }
            catch (Exception ex)
            {
                // 确保在UI线程上执行
                if (this.InvokeRequired)
                {
                    this.Invoke((MethodInvoker)delegate
                    {
                        ShowLoadingIndicator(false);
                        ShowStatusMessage($"处理失败: {ex.Message}");
                        MessageBox.Show($"处理顶部菜单点击失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    });
                }
                else
                {
                    ShowLoadingIndicator(false);
                    ShowStatusMessage($"处理失败: {ex.Message}");
                    MessageBox.Show($"处理顶部菜单点击失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        // 显示状态信息
        private void ShowStatusMessage(string message)
        {
            try
            {
                // 如果需要在非UI线程调用，则使用Invoke
                if (this.InvokeRequired)
                {
                    this.Invoke((MethodInvoker)delegate
                    {
                        ShowStatusMessage(message);
                    });
                    return;
                }

                // 如果有状态栏，可以在这里更新
                System.Diagnostics.Debug.WriteLine($"状态: {message}");

                // 可以添加一个状态栏标签来显示状态信息
                // 如: statusLabel.Text = message;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示状态信息失败: {ex.Message}");
            }
        }

        // 显示或隐藏加载指示器
        private void ShowLoadingIndicator(bool show)
        {
            try
            {
                if (this.InvokeRequired)
                {
                    this.Invoke((MethodInvoker)delegate
                    {
                        ShowLoadingIndicator(show);
                    });
                    return;
                }

                if (show)
                {
                    // 在加载时禁用左侧菜单控件
                    if (accordionControl1 != null)
                    {
                        accordionControl1.Enabled = false;
                    }

                    // 更新状态栏或其他UI元素来指示加载状态
                    // 这里可以根据实际UI设计添加加载指示器
                    this.Cursor = Cursors.WaitCursor;
                }
                else
                {
                    // 加载完成后恢复UI状态
                    if (accordionControl1 != null)
                    {
                        accordionControl1.Enabled = true;
                    }
                    this.Cursor = Cursors.Default;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"切换加载指示器状态失败: {ex.Message}");
            }
        }

        // 异步加载左侧菜单
        private async Task LoadLeftMenuAsync(int parentId)
        {
            try
            {
                // 临时打开调试模式
                _debugMode = true;

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"开始加载左侧菜单，父级ID: {parentId}");

                // 确保控件可见 - 确保在UI线程调用
                this.Invoke((MethodInvoker)delegate
                {
                    EnsureAccordionControlVisible();
                });

                // 从API获取子菜单数据
                var subMenus = await GetSubMenusAsync(parentId);

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"找到子菜单项: {subMenus.Count} 个");

                // 如果没有子菜单，创建一些测试项目
                if (!subMenus.Any())
                {
                    System.Diagnostics.Debug.WriteLine("没有子菜单，创建测试子菜单");
                    subMenus = new List<PermissionDto>
                    {
                        new PermissionDto { permissionId = parentId * 100 + 1, parentId = parentId, permissionName = "子菜单1", permissionUrl = "" },
                        new PermissionDto { permissionId = parentId * 100 + 2, parentId = parentId, permissionName = "子菜单2", permissionUrl = "" },
                        new PermissionDto { permissionId = parentId * 100 + 3, parentId = parentId, permissionName = "子菜单3", permissionUrl = "" }
                    };
                }

                // 修正：在UI线程上更新左侧菜单，不使用await this.Invoke
                if (this.InvokeRequired)
                {
                    this.Invoke((MethodInvoker)delegate
                    {
                        UpdateLeftMenu(subMenus);
                    });
                }
                else
                {
                    UpdateLeftMenu(subMenus);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载左侧菜单异常: {ex.Message}");
                throw; // 让上层处理这个异常
            }
        }

        // 新增辅助方法，用于在UI线程更新左侧菜单
        private void UpdateLeftMenu(List<PermissionDto> subMenus)
        {
            try
            {
                // 确认控件存在
                if (accordionControl1 == null)
                {
                    System.Diagnostics.Debug.WriteLine("错误: accordionControl1为空!");
                    return;
                }

                // 清空左侧菜单
                accordionControl1.BeginUpdate();
                accordionControl1.Elements.Clear();

                if (!subMenus.Any())
                {
                    // 如果没有子菜单，添加提示
                    var emptyElement = new DevExpress.XtraBars.Navigation.AccordionControlElement();
                    emptyElement.Text = "暂无子菜单";
                    emptyElement.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;
                    accordionControl1.Elements.Add(emptyElement);

                    System.Diagnostics.Debug.WriteLine($"已添加'暂无子菜单'提示");
                }
                else
                {
                    // 加载左侧菜单
                    foreach (var menu in subMenus)
                    {
                        int menuId = menu.GetId();
                        string menuName = menu.GetName();
                        string menuUrl = menu.GetUrl();

                        System.Diagnostics.Debug.WriteLine($"处理子菜单: {menuName} (ID: {menuId})");

                        // 直接创建一级菜单元素，不递归
                        var element = new DevExpress.XtraBars.Navigation.AccordionControlElement();
                        element.Text = menuName;
                        element.Tag = menu;
                        element.Name = $"Menu_{menuId}";
                        element.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;

                        // 添加点击事件
                        element.Click += (s, e) =>
                        {
                            var menuData = (PermissionDto)element.Tag;
                            NavigateToPage(menuData.GetName(), menuData.GetUrl());
                        };

                        // 添加到左侧菜单
                        accordionControl1.Elements.Add(element);
                        System.Diagnostics.Debug.WriteLine($"已添加菜单项: {menuName}");
                    }
                }

                // 默认展开所有菜单组
                System.Diagnostics.Debug.WriteLine("展开所有菜单组并结束更新");
                accordionControl1.ExpandAll();
                accordionControl1.EndUpdate();

                // 强制刷新UI
                Application.DoEvents();
                accordionControl1.Refresh();
                this.Refresh();

                // 确保控件可见
                accordionControl1.Visible = true;

                // 关闭调试模式
                _debugMode = false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"UI线程中加载菜单异常: {ex.Message}");
                throw;
            }
        }

        private void InitTopMenu()
        {
            try
            {
                // 确保在UI线程上执行
                if (this.InvokeRequired)
                {
                    this.Invoke((MethodInvoker)delegate
                    {
                        InitTopMenu();
                    });
                    return;
                }

                // 显示调试信息
                System.Diagnostics.Debug.WriteLine($"开始初始化顶部菜单，共 {_topMenus?.Count ?? 0} 项");

                if (_topMenus == null || _topMenus.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("错误: 没有可用的顶层菜单数据");
                    return;
                }

                // 移除原有的一级菜单按钮（保留用户信息和退出按钮）
                for (int i = fluentDesignFormControl1.TitleItemLinks.Count - 1; i >= 0; i--)
                {
                    var link = fluentDesignFormControl1.TitleItemLinks[i];
                    // 使用安全的类型检查，避免类型转换错误
                    if (link.Item is BarButtonItem btn && btn.Tag != null && btn.Tag is int)
                    {
                        fluentDesignFormControl1.TitleItemLinks.RemoveAt(i);
                        fluentDesignFormControl1.Items.Remove(btn);
                    }
                }

                // 添加所有顶层菜单项
                int index = 0;
                foreach (var menu in _topMenus)
                {
                    int menuId = menu.GetId();
                    string menuName = menu.GetName();

                    // 输出调试信息
                    System.Diagnostics.Debug.WriteLine($"添加顶部菜单: {menuName}, ID: {menuId}");

                    // 使用更清晰的标识方式
                    string menuText = menuName;

                    var btn = new BarButtonItem(fluentDesignFormControl1.Manager, menuText);
                    btn.Caption = menuName;
                    btn.Tag = menuId;

                    // 修复事件绑定，使用更安全的方式访问Item
                    int capturedId = menuId; // 捕获当前的ID值
                    btn.ItemClick += (sender, args) =>
                    {
                        try
                        {
                            System.Diagnostics.Debug.WriteLine($"点击了顶部菜单: {menuName}, ID: {capturedId}");
                            HandleTopMenuClick(capturedId);
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"菜单点击处理错误: {ex.Message}");
                        }
                    };

                    fluentDesignFormControl1.Items.Add(btn);
                    fluentDesignFormControl1.TitleItemLinks.Insert(index++, btn); // 按顺序插入
                }

                System.Diagnostics.Debug.WriteLine($"成功添加 {index} 个顶部菜单项");

                // 刷新控件
                fluentDesignFormControl1.Refresh();
                this.Refresh();
                Application.DoEvents();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化顶部菜单失败: {ex.Message}");
                MessageBox.Show($"初始化顶部菜单失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void HighlightTopMenuItem(int menuId)
        {
            try
            {
                // 确保在UI线程上执行
                if (this.InvokeRequired)
                {
                    this.Invoke((MethodInvoker)delegate
                    {
                        HighlightTopMenuItem(menuId);
                    });
                    return;
                }

                // 重置所有顶部菜单项样式
                foreach (BarItemLink link in fluentDesignFormControl1.TitleItemLinks)
                {
                    // 使用安全的类型检查
                    if (link != null && link.Item is BarButtonItem btn && btn.Tag != null && btn.Tag is int)
                    {
                        btn.Appearance.ForeColor = Color.Black;
                        btn.Appearance.Font = new Font(btn.Appearance.Font, FontStyle.Regular);
                    }
                }

                // 高亮选中的菜单项
                foreach (BarItemLink link in fluentDesignFormControl1.TitleItemLinks)
                {
                    // 使用安全的类型检查并确保所有条件都有效
                    if (link != null && link.Item is BarButtonItem btn &&
                        btn.Tag != null && btn.Tag is int id && id == menuId)
                    {
                        btn.Appearance.ForeColor = Color.FromArgb(0, 120, 215);
                        btn.Appearance.Font = new Font(btn.Appearance.Font, FontStyle.Bold);
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不中断程序流程
                System.Diagnostics.Debug.WriteLine($"高亮菜单项失败: {ex.Message}");
            }
        }

        // 异步创建菜单元素
        private async Task<DevExpress.XtraBars.Navigation.AccordionControlElement> CreateMenuElementAsync(PermissionDto menu)
        {
            try
            {
                // 创建菜单元素
                var element = new DevExpress.XtraBars.Navigation.AccordionControlElement();
                element.Text = menu.permissionName;
                element.Tag = menu;
                element.Name = $"Menu_{menu.permissionId}";

                // 从API获取子菜单
                var children = await GetSubMenusAsync(menu.permissionId);
                System.Diagnostics.Debug.WriteLine($"菜单 {menu.permissionName} 有 {children.Count} 个子菜单");

                // 根据是否有子菜单设置元素样式
                if (children.Any())
                {
                    // 这是一个菜单组
                    element.Style = DevExpress.XtraBars.Navigation.ElementStyle.Group;

                    // 创建子菜单元素列表
                    var childElements = new List<DevExpress.XtraBars.Navigation.AccordionControlElement>();
                    foreach (var child in children)
                    {
                        // 递归创建子菜单元素
                        var childElement = await CreateMenuElementAsync(child);
                        if (childElement != null)
                        {
                            childElements.Add(childElement);
                        }
                    }

                    // 安全地将子元素添加到父元素
                    foreach (var childElement in childElements)
                    {
                        element.Elements.Add(childElement);
                    }
                }
                else
                {
                    // 这是一个叶子菜单项
                    element.Style = DevExpress.XtraBars.Navigation.ElementStyle.Item;

                    // 设置菜单图标 (可选)
                    if (!string.IsNullOrEmpty(menu.permissionUrl))
                    {
                        // 使用安全的方式设置图标，避免跨线程访问
                        var iconUrl = menu.permissionUrl;
                        Image menuIcon = GetMenuIcon(iconUrl);
                        if (menuIcon != null)
                        {
                            element.Image = menuIcon;
                        }
                    }

                    // 为叶子节点添加点击事件 - 捕获值以避免闭包问题
                    var menuName = menu.permissionName;
                    var menuUrl = menu.permissionUrl;
                    element.Click += (s, e) =>
                    {
                        NavigateToPage(menuName, menuUrl);
                    };
                }

                return element;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建菜单元素异常: {ex.Message}");
                return null;
            }
        }

        private void NavigateToPage(string menuName, string permissionUrl)
        {
            try
            {
                // 确保在UI线程上执行
                if (this.InvokeRequired)
                {
                    this.Invoke((MethodInvoker)delegate
                    {
                        NavigateToPage(menuName, permissionUrl);
                    });
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"导航到页面: {menuName}, URL: {permissionUrl}");

                if (string.IsNullOrEmpty(permissionUrl))
                {
                    System.Diagnostics.Debug.WriteLine("URL为空，显示欢迎页");
                    ShowWelcome(menuName);
                    return;
                }

                // 清除旧的缓存，避免显示错误的页面
                _pageCache.Clear();
                System.Diagnostics.Debug.WriteLine("已清除页面缓存");

                // 检查页面缓存
                if (_pageCache.ContainsKey(permissionUrl))
                {
                    System.Diagnostics.Debug.WriteLine($"从缓存加载页面: {permissionUrl}");
                    ShowPage(_pageCache[permissionUrl], menuName);
                    return;
                }

                // 尝试加载对应的页面控件
                System.Diagnostics.Debug.WriteLine($"尝试查找类型: {permissionUrl}");
                Type formType = FindFormTypeByName(permissionUrl);
                System.Diagnostics.Debug.WriteLine($"查找结果: {(formType != null ? formType.FullName : "null")}");

                if (formType != null)
                {
                    System.Diagnostics.Debug.WriteLine($"找到类型: {formType.FullName}");

                    if (typeof(UserControl).IsAssignableFrom(formType))
                    {
                        System.Diagnostics.Debug.WriteLine($"创建控件实例: {formType.FullName}");
                        UserControl pageControl = (UserControl)Activator.CreateInstance(formType);
                        _pageCache[permissionUrl] = pageControl; // 缓存页面实例
                        ShowPage(pageControl, menuName);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"类型不是UserControl: {formType.FullName}");
                        MessageBox.Show($"页面 {permissionUrl} 不是有效的用户控件类型", "类型错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        ShowWelcome($"{menuName} (页面类型错误)");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"未找到类型: {permissionUrl}");
                    ShowWelcome($"{menuName} (页面 {permissionUrl} 未找到)");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导航到页面异常: {ex.Message}");
                MessageBox.Show($"加载页面失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ShowWelcome($"{menuName} (加载失败)");
            }
        }

        private Type FindFormTypeByName(string typeName)
        {
            try
            {
                // 处理斜杠格式的路径
                if (typeName.Contains("/"))
                {
                    // 将URL格式的路径转换为命名空间格式
                    typeName = typeName.Replace("/", ".");
                    System.Diagnostics.Debug.WriteLine($"路径中包含斜杠，已转换为: {typeName}");
                }

                // 在当前程序集中查找类型
                Assembly currentAssembly = Assembly.GetExecutingAssembly();

                // 检查是否包含命名空间
                if (typeName.Contains("."))
                {
                    // 包含命名空间的完整类型名称
                    string fullTypeName = $"MedicalDisinfectionSupplyCenter.{typeName}";
                    Type exactType = currentAssembly.GetType(fullTypeName);
                    if (exactType != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"找到完全匹配类型: {fullTypeName}");
                        return exactType;
                    }
                }

                // 如果没有找到精确匹配，尝试模糊匹配
                Type formType = currentAssembly.GetTypes()
                    .FirstOrDefault(t => t.Name.Equals(typeName, StringComparison.OrdinalIgnoreCase) ||
                                        t.FullName.Equals($"MedicalDisinfectionSupplyCenter.{typeName}", StringComparison.OrdinalIgnoreCase));

                if (formType != null)
                {
                    System.Diagnostics.Debug.WriteLine($"找到模糊匹配类型: {formType.FullName}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"未找到类型: {typeName}");

                    // 额外输出当前程序集中的所有类型，以帮助调试
                    System.Diagnostics.Debug.WriteLine("可用的类型:");
                    foreach (var type in currentAssembly.GetTypes()
                        .Where(t => typeof(UserControl).IsAssignableFrom(t))
                        .OrderBy(t => t.FullName))
                    {
                        System.Diagnostics.Debug.WriteLine($" - {type.FullName}");
                    }
                }

                return formType;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查找类型异常: {ex.Message}");
                return null;
            }
        }

        private void ShowPage(UserControl pageControl, string title)
        {
            // 确保在UI线程上执行
            if (this.InvokeRequired)
            {
                this.Invoke((MethodInvoker)delegate
                {
                    ShowPage(pageControl, title);
                });
                return;
            }

            fluentDesignFormContainer1.Controls.Clear();

            // 设置页面标题
            if (!string.IsNullOrEmpty(title))
            {
                this.Text = title;
            }

            // 显示页面控件
            pageControl.Dock = DockStyle.Fill;
            fluentDesignFormContainer1.Controls.Add(pageControl);
        }

        private Image GetMenuIcon(string permissionUrl)
        {
            // 根据不同的页面类型返回不同的图标
            // 这里可以根据实际需要实现，默认返回null
            return null;
        }

        private void ShowWelcome(string menuName = null)
        {
            // 确保在UI线程上执行
            if (this.InvokeRequired)
            {
                this.Invoke((MethodInvoker)delegate
                {
                    ShowWelcome(menuName);
                });
                return;
            }

            fluentDesignFormContainer1.Controls.Clear();

            // 设置主窗体标题
            this.Text = menuName == null ? "消毒供应中心管理系统" : $"消毒供应中心管理系统 - {menuName}";

            // 创建欢迎面板
            Panel welcomePanel = new Panel();
            welcomePanel.Dock = DockStyle.Fill;
            welcomePanel.BackColor = Color.White;

            // 添加欢迎标签
            var welcomeLabel = new Label();
            welcomeLabel.Text = menuName == null ? "欢迎使用消毒供应中心管理系统" : $"欢迎进入：{menuName}";
            welcomeLabel.Font = new Font("微软雅黑", 18, FontStyle.Bold);
            welcomeLabel.ForeColor = Color.FromArgb(0, 120, 215);
            welcomeLabel.AutoSize = false;
            welcomeLabel.BackColor = Color.Transparent;
            welcomeLabel.TextAlign = ContentAlignment.MiddleCenter;
            welcomeLabel.Dock = DockStyle.Fill;

            // 添加系统日期时间标签
            var dateTimeLabel = new Label();
            dateTimeLabel.Text = $"当前时间：{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}";
            dateTimeLabel.Font = new Font("微软雅黑", 10);
            dateTimeLabel.ForeColor = Color.Gray;
            dateTimeLabel.AutoSize = false;
            dateTimeLabel.TextAlign = ContentAlignment.MiddleCenter;
            dateTimeLabel.Height = 30;
            dateTimeLabel.Dock = DockStyle.Bottom;

            welcomePanel.Controls.Add(welcomeLabel);
            welcomePanel.Controls.Add(dateTimeLabel);

            fluentDesignFormContainer1.Controls.Add(welcomePanel);
        }

        // API响应包装类
        public class ApiResponse<T>
        {
            // 添加可能的字段别名以适应不同API响应格式
            [JsonProperty(PropertyName = "code")]
            public int code { get; set; }

            [JsonProperty(PropertyName = "msg")]
            public string msg { get; set; }

            [JsonProperty(PropertyName = "data")]
            public T data { get; set; }

            // 添加可能的备用字段
            [JsonProperty(PropertyName = "Code")]
            public int? Code { get; set; }

            [JsonProperty(PropertyName = "Msg")]
            public string Msg { get; set; }

            [JsonProperty(PropertyName = "Data")]
            public T Data { get; set; }

            // 帮助方法，获取实际的值
            public int GetCode() => code != 0 ? code : (Code ?? 0);
            public string GetMsg() => !string.IsNullOrEmpty(msg) ? msg : (Msg ?? string.Empty);
            public T GetData() => data != null ? data : Data;
        }

        public class PermissionDto
        {
            // 原始字段
            [JsonProperty(PropertyName = "permissionId")]
            public int permissionId { get; set; }

            [JsonProperty(PropertyName = "parentId")]
            public int parentId { get; set; }

            [JsonProperty(PropertyName = "permissionName")]
            public string permissionName { get; set; }

            [JsonProperty(PropertyName = "permissionUrl")]
            public string permissionUrl { get; set; }

            // 添加可能的备用字段名
            [JsonProperty(PropertyName = "id")]
            public int? Id { get; set; }

            [JsonProperty(PropertyName = "parent_id")]
            public int? ParentId { get; set; }

            [JsonProperty(PropertyName = "name")]
            public string Name { get; set; }

            [JsonProperty(PropertyName = "url")]
            public string Url { get; set; }

            // 帮助方法，获取实际的值
            public int GetId() => permissionId != 0 ? permissionId : (Id ?? 0);
            public int GetParentId() => parentId != 0 ? parentId : (ParentId ?? 0);
            public string GetName() => !string.IsNullOrEmpty(permissionName) ? permissionName : (Name ?? string.Empty);
            public string GetUrl() => !string.IsNullOrEmpty(permissionUrl) ? permissionUrl : (Url ?? string.Empty);
        }

        private void accordionControlElement1_Click(object sender, EventArgs e)
        {

        }

        // 辅助方法，显示调试消息
        private void DebugMessage(string message)
        {
            // 只输出到调试窗口，不显示消息框
            System.Diagnostics.Debug.WriteLine(message);

            // 在调试模式下才显示消息框
            if (_debugMode)
            {
                MessageBox.Show(message, "调试信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        // 根据父级ID获取子菜单
        private async Task<List<PermissionDto>> GetSubMenusAsync(int parentId)
        {
            try
            {
                // 添加超时和重试机制
                int maxRetries = 2;
                int currentRetry = 0;

                while (currentRetry <= maxRetries)
                {
                    try
                    {
                        using (var client = new HttpClient())
                        {
                            // 设置超时
                            client.Timeout = TimeSpan.FromSeconds(10);

                            // 使用GET请求
                            string url = $"{API_BASE_URL}Handle?parentId={parentId}";
                            System.Diagnostics.Debug.WriteLine($"请求子菜单URL: {url}");

                            // 显示调试信息
                            System.Diagnostics.Debug.WriteLine($"正在请求 {url}");

                            var response = await client.GetStringAsync(url);
                            System.Diagnostics.Debug.WriteLine($"子菜单API响应: {response.Substring(0, Math.Min(response.Length, 100))}...");

                            // 解析API响应包装对象
                            var apiResponse = JsonConvert.DeserializeObject<ApiResponse<List<PermissionDto>>>(response);

                            // 检查API调用是否成功
                            if (apiResponse != null && apiResponse.GetCode() == 200)
                            {
                                var result = apiResponse.GetData() ?? new List<PermissionDto>();
                                System.Diagnostics.Debug.WriteLine($"成功获取 {result.Count} 个子菜单项 (parentId={parentId})");
                                return result;
                            }
                            else
                            {
                                string errorMsg = apiResponse != null ? apiResponse.GetMsg() : "未知错误";
                                System.Diagnostics.Debug.WriteLine($"获取子菜单API错误: {errorMsg}");

                                if (currentRetry == maxRetries)
                                {
                                    // 最后一次重试失败，创建一些测试数据而不是返回空列表
                                    return CreateTestSubMenus(parentId);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"获取子菜单请求异常 (尝试 {currentRetry + 1}/{maxRetries + 1}): {ex.Message}");

                        if (currentRetry == maxRetries)
                        {
                            // 最后一次重试也失败，创建一些测试数据而不是返回空列表
                            return CreateTestSubMenus(parentId);
                        }
                    }

                    // 增加重试次数，并等待一段时间
                    currentRetry++;
                    if (currentRetry <= maxRetries)
                    {
                        await Task.Delay(500 * currentRetry); // 递增的等待时间
                    }
                }

                return CreateTestSubMenus(parentId); // 防止代码路径漏洞
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取子菜单总异常: {ex.Message}");
                return CreateTestSubMenus(parentId); // 返回一些测试数据而不是空列表
            }
        }

        // 创建测试子菜单项，用于API不可用时的调试
        private List<PermissionDto> CreateTestSubMenus(int parentId)
        {
            System.Diagnostics.Debug.WriteLine($"创建ID为 {parentId} 的测试子菜单");

            // 根据不同的父级ID创建不同的子菜单
            if (parentId == 3) // 设备管理菜单
            {
                return new List<PermissionDto>
                {
                    new PermissionDto { permissionId = parentId * 100 + 1, parentId = parentId, permissionName = "设备入库", permissionUrl = "BasicManagement.DeviceInboundControl" },
                    new PermissionDto { permissionId = parentId * 100 + 2, parentId = parentId, permissionName = "设备出库", permissionUrl = "BasicManagement/DeviceOutboundControl" }, // 使用斜杠格式
                    new PermissionDto { permissionId = parentId * 100 + 3, parentId = parentId, permissionName = "设备字典", permissionUrl = "BasicManagement.DeviceDictionaryControl" }
                };
            }
            else if (parentId == 4) // 基础数据菜单
            {
                return new List<PermissionDto>
                {
                    new PermissionDto { permissionId = parentId * 100 + 1, parentId = parentId, permissionName = "器械包字典", permissionUrl = "BasicManagement.EquipmentPackageDictionary" },
                    new PermissionDto { permissionId = parentId * 100 + 2, parentId = parentId, permissionName = "设备字典", permissionUrl = "BasicManagement.DeviceDictionary" },
                    new PermissionDto { permissionId = parentId * 100 + 3, parentId = parentId, permissionName = "货架字典", permissionUrl = "BasicManagement.ShelfDictionary" },
                    new PermissionDto { permissionId = parentId * 100 + 4, parentId = parentId, permissionName = "回收申请", permissionUrl = "BasicManagement.RecyclingApplication" },
                    new PermissionDto { permissionId = parentId * 100 + 5, parentId = parentId, permissionName = "领用申请", permissionUrl = "BasicManagement.ApplicationRequisition" },
                    new PermissionDto { permissionId = parentId * 100 + 6, parentId = parentId, permissionName = "签收管理", permissionUrl = "BasicManagement.ShelfDictionary" },
                    new PermissionDto { permissionId = parentId * 100 + 7, parentId = parentId, permissionName = "使用管理", permissionUrl = "DepartmentManagement.UseManagement" },
                    new PermissionDto { permissionId = parentId * 100 + 8, parentId = parentId, permissionName = "存放管理", permissionUrl = "BasicManagement.StorageManagement" },
                    new PermissionDto { permissionId = parentId * 100 + 9, parentId = parentId, permissionName = "发放使用", permissionUrl = "BasicManagement.DistributeUse" },
                    new PermissionDto { permissionId = parentId * 100 + 10, parentId = parentId, permissionName = "入库管理", permissionUrl = "BasicManagement.WarehouseManagement" },
                    new PermissionDto { permissionId = parentId * 100 + 11, parentId = parentId, permissionName = "出库管理", permissionUrl = "BasicManagement.OutboundManagement" }
                };
            }
            else if (parentId == 1) // 系统管理菜单 - 已移除用户管理、角色管理、权限管理
            {
                return new List<PermissionDto>
                {
                    new PermissionDto { permissionId = parentId * 100 + 1, parentId = parentId, permissionName = "系统设置", permissionUrl = "" },
                    new PermissionDto { permissionId = parentId * 100 + 2, parentId = parentId, permissionName = "数据备份", permissionUrl = "" },
                    new PermissionDto { permissionId = parentId * 100 + 3, parentId = parentId, permissionName = "系统日志", permissionUrl = "" }
                };
            }
            else if (parentId == 2) // 报表管理菜单
            {
                return new List<PermissionDto>
                {
                    new PermissionDto { permissionId = parentId * 100 + 1, parentId = parentId, permissionName = "入库报表", permissionUrl = "ReportManagement.InboundReportControl" },
                    new PermissionDto { permissionId = parentId * 100 + 2, parentId = parentId, permissionName = "出库报表", permissionUrl = "ReportManagement/OutboundReportControl" }, // 使用斜杠格式
                    new PermissionDto { permissionId = parentId * 100 + 3, parentId = parentId, permissionName = "库存报表", permissionUrl = "ReportManagement.InventoryReportControl" }
                };
            }
            else // 其他菜单的默认子菜单
            {
                return new List<PermissionDto>
                {
                    new PermissionDto { permissionId = parentId * 100 + 1, parentId = parentId, permissionName = $"子菜单1 ({parentId})", permissionUrl = "" },
                    new PermissionDto { permissionId = parentId * 100 + 2, parentId = parentId, permissionName = $"子菜单2 ({parentId})", permissionUrl = "" },
                    new PermissionDto { permissionId = parentId * 100 + 3, parentId = parentId, permissionName = $"子菜单3 ({parentId})", permissionUrl = "" }
                };
            }
        }
    }
}
