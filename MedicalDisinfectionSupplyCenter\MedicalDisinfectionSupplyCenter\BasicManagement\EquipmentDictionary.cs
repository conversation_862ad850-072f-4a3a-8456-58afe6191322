using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraLayout;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MedicalDisinfectionSupplyCenter.BasicManagement
{
    /// <summary>
    /// 器械字典管理界面
    /// </summary>
    public partial class EquipmentDictionary : UserControl
    {
        #region 常量与API客户端
        private const string API_BASE_URL = "http://localhost:5172/api/BasicManagement";
        //private const string API_BASE_URL = "http://***********:4050/api/BasicManagement";
        private const string API_WRITE_BASE_URL = "http://localhost:5192/api/BasicManagement";
        //private const string API_WRITE_BASE_URL = "http://***********:4060/api/BasicManagement";
        private static readonly HttpClient httpClient = new HttpClient() { Timeout = TimeSpan.FromSeconds(30) };
        #endregion

        #region 控件声明
        private LayoutControl layoutControl;
        private LayoutControlGroup rootGroup;

        // 主面板控件
        private PanelControl panelToolbar;
        private ComboBoxEdit comboApparatusType;
        private CheckEdit chkShowDisabled;
        private SimpleButton btnQuery;
        private SimpleButton btnAdd;
        private GridControl gridApparatus;
        private GridView gridViewApparatus;
        private LabelControl lblTotal;

        // 分页控件
        private PanelControl panelPagination;
        private SimpleButton btnPrevPage;
        private SimpleButton btnNextPage;
        private LabelControl lblPageInfo;
        private ComboBoxEdit comboPageSize;
        private LabelControl lblPageSize;

        // 分页变量
        private int _currentPageIndex = 1;
        private int _pageSize = 20;
        private int _totalCount = 0;
        private int _totalPages = 0;
        #endregion

        /// <summary>
        /// 初始化器械字典界面
        /// </summary>
        public EquipmentDictionary()
        {
            try
            {
                InitializeComponent();
                InitializeControls();
                InitializeEvents();

                // 使用Load事件来执行异步操作，避免在构造函数中直接调用异步方法
                this.Load += EquipmentDictionary_Load;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化器械字典界面时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 页面加载事件处理
        /// </summary>
        private async void EquipmentDictionary_Load(object sender, EventArgs e)
        {
            try
            {
                // 在页面加载时执行异步操作
                await LoadApparatusTypeComboAsync();

                // 初始化分页控件状态
                UpdatePaginationControls();

                // 加载器械数据
                await QueryApparatusAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载器械字典数据时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化所有控件
        /// </summary>
        private void InitializeControls()
        {
            // 主布局控件
            layoutControl = new LayoutControl();
            layoutControl.Dock = DockStyle.Fill;
            layoutControl.Name = "layoutControl";

            // 创建主面板
            var mainPanel = new PanelControl();
            mainPanel.Name = "mainPanel";
            mainPanel.Dock = DockStyle.Fill;

            // ===== 工具栏和表格 =====

            // 工具栏面板 - 增加高度以提供更好间距
            panelToolbar = new PanelControl();
            panelToolbar.Name = "panelToolbar";
            panelToolbar.Height = 80;
            panelToolbar.Dock = DockStyle.Top;
            panelToolbar.Padding = new Padding(10, 10, 10, 10);

            // 器械类型标签
            var lblApparatusType = new LabelControl();
            lblApparatusType.Location = new Point(20, 28);
            lblApparatusType.Size = new Size(70, 28);
            lblApparatusType.Text = "器械类型：";
            lblApparatusType.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;

            // 器械类型下拉框
            comboApparatusType = new ComboBoxEdit();
            comboApparatusType.Name = "comboApparatusType";
            comboApparatusType.Location = new Point(100, 25);
            comboApparatusType.Size = new Size(150, 28);
            comboApparatusType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            // 不在初始化时设置SelectedIndex，等数据加载后再设置

            // 显示禁用项复选框
            chkShowDisabled = new CheckEdit();
            chkShowDisabled.Name = "chkShowDisabled";
            chkShowDisabled.Location = new Point(270, 25);
            chkShowDisabled.Size = new Size(120, 28);
            chkShowDisabled.Text = "显示禁用项";
            chkShowDisabled.Checked = true; // 默认设置为选中状态

            // 查询按钮
            btnQuery = new SimpleButton();
            btnQuery.Name = "btnQuery";
            btnQuery.Location = new Point(400, 25);
            btnQuery.Size = new Size(80, 28);
            btnQuery.Text = "查询";

            // 添加按钮
            btnAdd = new SimpleButton();
            btnAdd.Name = "btnAdd";
            btnAdd.Location = new Point(490, 25);
            btnAdd.Size = new Size(80, 28);
            btnAdd.Text = "添加";

            // 将控件添加到面板
            panelToolbar.Controls.Add(lblApparatusType);
            panelToolbar.Controls.Add(comboApparatusType);
            panelToolbar.Controls.Add(chkShowDisabled);
            panelToolbar.Controls.Add(btnQuery);
            panelToolbar.Controls.Add(btnAdd);

            // 器械列表表格
            gridApparatus = new GridControl();
            gridApparatus.Name = "gridApparatus";
            gridApparatus.Dock = DockStyle.Fill;
            gridViewApparatus = new GridView(gridApparatus);
            gridViewApparatus.Name = "gridViewApparatus";
            gridApparatus.MainView = gridViewApparatus;

            // 设置表格列
            SetupGridColumns();

            // 分页面板
            panelPagination = new PanelControl();
            panelPagination.Name = "panelPagination";
            panelPagination.Height = 40;
            panelPagination.Dock = DockStyle.Bottom;
            panelPagination.Padding = new Padding(10, 5, 10, 5);

            // 上一页按钮
            btnPrevPage = new SimpleButton();
            btnPrevPage.Name = "btnPrevPage";
            btnPrevPage.Text = "上一页";
            btnPrevPage.Location = new Point(10, 8);
            btnPrevPage.Size = new Size(70, 24);
            btnPrevPage.Enabled = false;

            // 下一页按钮
            btnNextPage = new SimpleButton();
            btnNextPage.Name = "btnNextPage";
            btnNextPage.Text = "下一页";
            btnNextPage.Location = new Point(90, 8);
            btnNextPage.Size = new Size(70, 24);
            btnNextPage.Enabled = false;

            // 页码信息标签
            lblPageInfo = new LabelControl();
            lblPageInfo.Name = "lblPageInfo";
            lblPageInfo.Text = "第1页/共1页";
            lblPageInfo.Location = new Point(170, 12);
            lblPageInfo.Size = new Size(100, 16);

            // 每页大小标签
            lblPageSize = new LabelControl();
            lblPageSize.Name = "lblPageSize";
            lblPageSize.Text = "每页显示：";
            lblPageSize.Location = new Point(280, 12);
            lblPageSize.Size = new Size(60, 16);

            // 每页大小下拉框
            comboPageSize = new ComboBoxEdit();
            comboPageSize.Name = "comboPageSize";
            comboPageSize.Location = new Point(345, 8);
            comboPageSize.Size = new Size(60, 24);
            comboPageSize.Properties.TextEditStyle = TextEditStyles.DisableTextEditor;
            comboPageSize.Properties.Items.AddRange(new object[] { "10", "20", "50", "100" });
            comboPageSize.SelectedIndex = 1; // 默认选择20

            // 合计标签
            lblTotal = new LabelControl();
            lblTotal.Name = "lblTotal";
            lblTotal.Text = "合计：0条";
            lblTotal.Location = new Point(420, 12);
            lblTotal.Size = new Size(100, 16);

            // 将控件添加到分页面板
            panelPagination.Controls.Add(btnPrevPage);
            panelPagination.Controls.Add(btnNextPage);
            panelPagination.Controls.Add(lblPageInfo);
            panelPagination.Controls.Add(lblPageSize);
            panelPagination.Controls.Add(comboPageSize);
            panelPagination.Controls.Add(lblTotal);

            // 添加控件到主面板（按正确顺序）
            mainPanel.Controls.Add(panelPagination); // 底部分页面板
            mainPanel.Controls.Add(gridApparatus);    // 填充剩余空间
            mainPanel.Controls.Add(panelToolbar);     // 顶部工具栏

            // 将主面板添加到布局控件
            layoutControl.Controls.Add(mainPanel);

            // 将主控件添加到用户控件
            this.Controls.Add(layoutControl);

            // 配置主布局组
            rootGroup = new LayoutControlGroup();
            rootGroup.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            layoutControl.Root = rootGroup;

            // 配置主面板布局项
            var layoutControlItemMain = new LayoutControlItem();
            layoutControlItemMain.Control = mainPanel;
            layoutControlItemMain.TextVisible = false;
            layoutControl.Root.AddItem(layoutControlItemMain);
        }

        /// <summary>
        /// 设置表格列
        /// </summary>
        private void SetupGridColumns()
        {
            // 清除现有列
            gridViewApparatus.Columns.Clear();

            // 添加列
            gridViewApparatus.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "ID",
                FieldName = "Id",
                Visible = false
            });

            gridViewApparatus.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "器械图片",
                FieldName = "ApparatusImage",
                Visible = true,
                Width = 80
            });

            gridViewApparatus.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "器械编码",
                FieldName = "ApparatusCode",
                Visible = true,
                Width = 100
            });

            gridViewApparatus.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "器械名称",
                FieldName = "ApparatusName",
                Visible = true,
                Width = 120
            });

            gridViewApparatus.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "单位",
                FieldName = "Unit",
                Visible = true,
                Width = 60
            });

            gridViewApparatus.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "单价",
                FieldName = "Price",
                Visible = true,
                Width = 80
            });

            gridViewApparatus.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "生产厂商",
                FieldName = "Manufacturer",
                Visible = true,
                Width = 120
            });

            gridViewApparatus.Columns.Add(new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "状态",
                FieldName = "StatusName",
                Visible = true,
                Width = 60
            });

            // 添加操作列
            var colOperation = new DevExpress.XtraGrid.Columns.GridColumn
            {
                Caption = "操作",
                FieldName = "Operation",
                Visible = true,
                Width = 120,
                UnboundType = DevExpress.Data.UnboundColumnType.Object
            };

            // 创建按钮编辑器
            var repositoryItemButtonEdit = new RepositoryItemButtonEdit();
            repositoryItemButtonEdit.TextEditStyle = TextEditStyles.HideTextEditor;
            repositoryItemButtonEdit.Buttons[0].Caption = "编辑";
            repositoryItemButtonEdit.Buttons[0].Kind = ButtonPredefines.Glyph;
            repositoryItemButtonEdit.Buttons.Add(new EditorButton(ButtonPredefines.Glyph));
            repositoryItemButtonEdit.Buttons[1].Caption = "删除";
            repositoryItemButtonEdit.Buttons[1].Kind = ButtonPredefines.Glyph;
            repositoryItemButtonEdit.ButtonClick += RepositoryItemButtonEdit_ButtonClick;

            colOperation.ColumnEdit = repositoryItemButtonEdit;
            gridViewApparatus.Columns.Add(colOperation);

            // 设置表格属性
            gridViewApparatus.OptionsBehavior.Editable = false;
            gridViewApparatus.OptionsView.ShowGroupPanel = false;
            gridViewApparatus.OptionsView.ShowIndicator = true;
            gridViewApparatus.OptionsView.EnableAppearanceEvenRow = true;
            gridViewApparatus.OptionsView.EnableAppearanceOddRow = true;
        }

        /// <summary>
        /// 初始化事件
        /// </summary>
        private void InitializeEvents()
        {
            // 查询按钮点击事件
            btnQuery.Click += async (sender, e) =>
            {
                _currentPageIndex = 1; // 重置到第一页
                await QueryApparatusAsync();
            };

            // 添加按钮点击事件
            btnAdd.Click += BtnAdd_Click;

            // 表格双击事件
            gridViewApparatus.DoubleClick += GridViewApparatus_DoubleClick;

            // 分页事件
            btnPrevPage.Click += async (sender, e) =>
            {
                if (_currentPageIndex > 1)
                {
                    _currentPageIndex--;
                    await QueryApparatusAsync();
                }
            };

            btnNextPage.Click += async (sender, e) =>
            {
                if (_currentPageIndex < _totalPages)
                {
                    _currentPageIndex++;
                    await QueryApparatusAsync();
                }
            };

            // 每页大小变更事件
            comboPageSize.SelectedIndexChanged += async (sender, e) =>
            {
                if (int.TryParse(comboPageSize.SelectedItem?.ToString(), out int newPageSize))
                {
                    _pageSize = newPageSize;
                    _currentPageIndex = 1; // 重置到第一页
                    await QueryApparatusAsync();
                }
            };

            // 显示禁用项复选框变更事件
            chkShowDisabled.CheckedChanged += async (sender, e) =>
            {
                _currentPageIndex = 1; // 重置到第一页
                await QueryApparatusAsync();
            };
        }



        /// <summary>
        /// 表格双击事件处理
        /// </summary>
        private void GridViewApparatus_DoubleClick(object sender, EventArgs e)
        {
            // 获取当前选中行
            var focusedRowHandle = gridViewApparatus.FocusedRowHandle;
            if (focusedRowHandle < 0) return;

            // 获取选中的器械数据
            var apparatus = gridViewApparatus.GetRow(focusedRowHandle) as ApparatusListDto;
            if (apparatus == null) return;

            // 打开编辑窗口
            EditApparatus(apparatus);
        }

        /// <summary>
        /// 按钮编辑器点击事件
        /// </summary>
        private void RepositoryItemButtonEdit_ButtonClick(object sender, ButtonPressedEventArgs e)
        {
            // 获取当前行
            var rowHandle = gridViewApparatus.FocusedRowHandle;
            if (rowHandle < 0) return;

            // 获取当前行数据
            var apparatus = gridViewApparatus.GetRow(rowHandle) as ApparatusListDto;
            if (apparatus == null) return;

            // 根据按钮索引执行不同操作
            if (e.Button.Index == 0) // 编辑按钮
            {
                EditApparatus(apparatus);
            }
            else if (e.Button.Index == 1) // 删除按钮
            {
                DeleteApparatus(apparatus);
            }
        }

        /// <summary>
        /// 添加按钮点击事件
        /// </summary>
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            // 弹出新增器械窗口
            using (var addForm = new Form())
            {
                addForm.Text = "新增器械";
                addForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                addForm.StartPosition = FormStartPosition.CenterParent;
                addForm.Width = 500;
                addForm.Height = 520;
                addForm.MaximizeBox = false;
                addForm.MinimizeBox = false;
                addForm.BackColor = Color.White;

                // 器械分类
                var lblCategory = new Label() { Text = "器械分类", Left = 30, Top = 40, Width = 80, Font = new Font("微软雅黑", 9) };
                var cmbCategory = new System.Windows.Forms.ComboBox() { Left = 120, Top = 38, Width = 320, Height = 25, DropDownStyle = ComboBoxStyle.DropDownList, Font = new Font("微软雅黑", 9) };

                // 加载固定的器械分类数据
                cmbCategory.Items.Add(new ApparatusTypeComboItem { Id = 1, Name = "普通器械" });
                cmbCategory.Items.Add(new ApparatusTypeComboItem { Id = 2, Name = "特殊器械" });

                // 默认选择第一个分类（普通器械）
                cmbCategory.SelectedIndex = 0;

                // *器械编码
                var lblCodeStar = new Label() { Text = "*", ForeColor = System.Drawing.Color.Red, Left = 18, Top = 80, Width = 10, Font = new Font("微软雅黑", 9) };
                var lblCode = new Label() { Text = "器械编码", Left = 30, Top = 80, Width = 80, Font = new Font("微软雅黑", 9) };
                var txtCode = new TextBox() { Left = 120, Top = 78, Width = 320, Height = 25, Font = new Font("微软雅黑", 9) };

                // *器械名称
                var lblNameStar = new Label() { Text = "*", ForeColor = System.Drawing.Color.Red, Left = 18, Top = 120, Width = 10, Font = new Font("微软雅黑", 9) };
                var lblName = new Label() { Text = "器械名称", Left = 30, Top = 120, Width = 80, Font = new Font("微软雅黑", 9) };
                var txtName = new TextBox() { Left = 120, Top = 118, Width = 320, Height = 25, Font = new Font("微软雅黑", 9) };

                // *器械型号
                var lblModelStar = new Label() { Text = "*", ForeColor = System.Drawing.Color.Red, Left = 18, Top = 160, Width = 10, Font = new Font("微软雅黑", 9) };
                var lblModel = new Label() { Text = "器械型号", Left = 30, Top = 160, Width = 80, Font = new Font("微软雅黑", 9) };
                var txtModel = new TextBox() { Left = 120, Top = 158, Width = 320, Height = 25, Font = new Font("微软雅黑", 9) };

                // *单位
                var lblUnitStar = new Label() { Text = "*", ForeColor = System.Drawing.Color.Red, Left = 18, Top = 200, Width = 10, Font = new Font("微软雅黑", 9) };
                var lblUnit = new Label() { Text = "单位", Left = 30, Top = 200, Width = 80, Font = new Font("微软雅黑", 9) };
                var txtUnit = new TextBox() { Left = 120, Top = 198, Width = 320, Height = 25, Font = new Font("微软雅黑", 9) };

                // 单价
                var lblPrice = new Label() { Text = "单价", Left = 30, Top = 240, Width = 80, Font = new Font("微软雅黑", 9) };
                var txtPrice = new TextBox() { Left = 120, Top = 238, Width = 320, Height = 25, Font = new Font("微软雅黑", 9) };

                // 耐用属性
                var lblDurable = new Label() { Text = "耐用属性", Left = 30, Top = 280, Width = 80, Font = new Font("微软雅黑", 9) };
                var rbDurable = new RadioButton() { Text = "耐用品", Left = 120, Top = 278, Width = 100, Checked = true, Font = new Font("微软雅黑", 9) };
                var rbDisposable = new RadioButton() { Text = "一次性用品", Left = 240, Top = 278, Width = 120, Font = new Font("微软雅黑", 9) };

                // 退出按钮
                var btnCancel = new Button() { Text = "退出", Left = 250, Top = 350, Width = 80, Height = 35, FlatStyle = FlatStyle.Flat, Font = new Font("微软雅黑", 9) };
                btnCancel.FlatAppearance.BorderColor = Color.Gray;
                btnCancel.FlatAppearance.BorderSize = 1;
                btnCancel.BackColor = Color.White;

                // 保存按钮
                var btnSave = new Button() { Text = "保存", Left = 350, Top = 350, Width = 80, Height = 35, BackColor = Color.FromArgb(24, 144, 255), ForeColor = Color.White, FlatStyle = FlatStyle.Flat, Font = new Font("微软雅黑", 9) };
                btnSave.FlatAppearance.BorderSize = 0;

                btnCancel.Click += (s, ev) => addForm.DialogResult = DialogResult.Cancel;

                btnSave.Click += async (s, ev) =>
                {
                    // 必填校验
                    if (string.IsNullOrWhiteSpace(txtCode.Text))
                    {
                        MessageBox.Show("器械编码不能为空！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                    if (string.IsNullOrWhiteSpace(txtName.Text))
                    {
                        MessageBox.Show("器械名称不能为空！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                    if (string.IsNullOrWhiteSpace(txtModel.Text))
                    {
                        MessageBox.Show("器械型号不能为空！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                    if (string.IsNullOrWhiteSpace(txtUnit.Text))
                    {
                        MessageBox.Show("单位不能为空！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // 单价校验（可选字段）
                    decimal price = 0;
                    if (!string.IsNullOrWhiteSpace(txtPrice.Text) && !decimal.TryParse(txtPrice.Text, out price))
                    {
                        MessageBox.Show("单价必须是有效的数字！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // 获取选中的分类ID
                    int categoryId = 0;
                    if (cmbCategory.SelectedItem is ApparatusTypeComboItem selectedType)
                    {
                        categoryId = selectedType.Id;
                    }
                    else
                    {
                        MessageBox.Show("请选择器械分类！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // 获取耐用属性
                    string durableProperty = rbDurable.Checked ? "耐用品" : "一次性用品";

                    // 构造请求对象
                    var newApparatus = new
                    {
                        ApparatusImage = "", // 器械图片路径，暂时设为空
                        ApparatusCode = txtCode.Text.Trim(),
                        ApparatusName = txtName.Text.Trim(),
                        Unit = txtUnit.Text.Trim(),
                        Price = price,
                        Manufacturer = "", // 生产厂商字段已移除，设为空
                        CategoryId = categoryId,
                        Model = txtModel.Text.Trim(),
                        DurableProperty = durableProperty
                    };

                    try
                    {
                        // 检查HttpClient状态
                        if (httpClient == null)
                        {
                            MessageBox.Show("网络客户端未初始化，请重启应用程序。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }

                        var url = $"{API_WRITE_BASE_URL}/AddApparatus/AddApparatus";
                        var jsonContent = JsonConvert.SerializeObject(newApparatus);

                        // 添加调试信息
                        System.Diagnostics.Debug.WriteLine($"API请求URL: {url}");
                        System.Diagnostics.Debug.WriteLine($"请求数据: {jsonContent}");

                        var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                        var response = await httpClient.PostAsync(url, content);

                        System.Diagnostics.Debug.WriteLine($"响应状态码: {response.StatusCode}");

                        if (response.IsSuccessStatusCode)
                        {
                            var responseContent = await response.Content.ReadAsStringAsync();
                            System.Diagnostics.Debug.WriteLine($"响应内容: {responseContent}");

                            try
                            {
                                // 使用动态解析来处理不同的响应格式
                                var dynamicResult = JsonConvert.DeserializeObject<dynamic>(responseContent);

                                if (dynamicResult != null)
                                {
                                    // 获取code字段，可能是数字或字符串
                                    var codeValue = dynamicResult.code?.ToString();
                                    var msgValue = dynamicResult.msg?.ToString();
                                    var dataValue = dynamicResult.data;

                                    // 检查是否成功（code为200或"Success"）
                                    bool isSuccess = (codeValue == "200" || codeValue == "Success");

                                    // 如果有data字段，验证其为有效的正整数
                                    if (isSuccess && dataValue != null)
                                    {
                                        if (int.TryParse(dataValue.ToString(), out int dataInt))
                                        {
                                            isSuccess = dataInt > 0;
                                        }
                                        else
                                        {
                                            isSuccess = true; // 如果data不是数字，仍然认为成功
                                        }
                                    }

                                    if (isSuccess)
                                    {
                                        MessageBox.Show("新增器械成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                        addForm.DialogResult = DialogResult.OK;
                                    }
                                    else
                                    {
                                        var errorMsg = msgValue ?? "未知错误";
                                        MessageBox.Show($"新增失败: {errorMsg}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                    }
                                }
                                else
                                {
                                    MessageBox.Show("响应数据为空", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                            catch (JsonException jsonEx)
                            {
                                MessageBox.Show($"响应数据解析失败: {jsonEx.Message}\n原始响应: {responseContent}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        else
                        {
                            var errorContent = await response.Content.ReadAsStringAsync();
                            MessageBox.Show($"API请求失败\n状态码: {response.StatusCode}\n错误信息: {errorContent}\n请求URL: {url}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (HttpRequestException httpEx)
                    {
                        MessageBox.Show($"网络连接失败: {httpEx.Message}\n请检查API服务是否启动\n请求URL: {API_WRITE_BASE_URL}/AddApparatus", "网络错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    catch (TaskCanceledException timeoutEx)
                    {
                        MessageBox.Show($"请求超时: {timeoutEx.Message}\n请检查网络连接或API服务响应速度", "超时错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"未知异常: {ex.Message}\n异常类型: {ex.GetType().Name}\n堆栈跟踪: {ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                };

                // 添加控件到表单
                addForm.Controls.AddRange(new Control[] {
                    lblCategory, cmbCategory,
                    lblCodeStar, lblCode, txtCode,
                    lblNameStar, lblName, txtName,
                    lblModelStar, lblModel, txtModel,
                    lblUnitStar, lblUnit, txtUnit,
                    lblPrice, txtPrice,
                    lblDurable, rbDurable, rbDisposable,
                    btnCancel, btnSave
                });

                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    // 新增成功后刷新主表格，保持当前分页
                    _ = Task.Run(async () => await QueryApparatusAsync());
                }
            }
        }

        /// <summary>
        /// 编辑器械
        /// </summary>
        private void EditApparatus(ApparatusListDto apparatus)
        {
            using (var editForm = new Form())
            {
                editForm.Text = "器械编辑";
                editForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                editForm.StartPosition = FormStartPosition.CenterParent;
                editForm.Width = 430;
                editForm.Height = 450;
                editForm.MaximizeBox = false;
                editForm.MinimizeBox = false;

                // 器械分类
                var lblCategory = new Label() { Text = "器械分类", Left = 30, Top = 30, Width = 80 };
                var cmbCategory = new System.Windows.Forms.ComboBox() { Left = 120, Top = 28, Width = 250, DropDownStyle = ComboBoxStyle.DropDownList };

                // 加载分类数据
                foreach (var item in comboApparatusType.Properties.Items)
                {
                    if (item is ApparatusTypeComboItem typeItem)
                    {
                        cmbCategory.Items.Add(typeItem);
                        if (typeItem.Id == apparatus.CategoryId)
                        {
                            cmbCategory.SelectedItem = typeItem;
                        }
                    }
                }

                // *器械编码
                var lblCodeStar = new Label() { Text = "*", ForeColor = System.Drawing.Color.Red, Left = 18, Top = 70, Width = 10 };
                var lblCode = new Label() { Text = "器械编码", Left = 30, Top = 70, Width = 80 };
                var txtCode = new TextBox() { Left = 120, Top = 68, Width = 250, Text = apparatus.ApparatusCode };

                // *器械名称
                var lblNameStar = new Label() { Text = "*", ForeColor = System.Drawing.Color.Red, Left = 18, Top = 110, Width = 10 };
                var lblName = new Label() { Text = "器械名称", Left = 30, Top = 110, Width = 80 };
                var txtName = new TextBox() { Left = 120, Top = 108, Width = 250, Text = apparatus.ApparatusName };

                // 单位
                var lblUnit = new Label() { Text = "单位", Left = 30, Top = 150, Width = 80 };
                var txtUnit = new TextBox() { Left = 120, Top = 148, Width = 250, Text = apparatus.Unit };

                // 单价
                var lblPrice = new Label() { Text = "单价", Left = 30, Top = 190, Width = 80 };
                var txtPrice = new TextBox() { Left = 120, Top = 188, Width = 250, Text = apparatus.Price.ToString() };

                // 生产厂商
                var lblManufacturer = new Label() { Text = "生产厂商", Left = 30, Top = 230, Width = 80 };
                var txtManufacturer = new TextBox() { Left = 120, Top = 228, Width = 250, Text = apparatus.Manufacturer };

                // 型号
                var lblModel = new Label() { Text = "型号", Left = 30, Top = 270, Width = 80 };
                var txtModel = new TextBox() { Left = 120, Top = 268, Width = 250, Text = apparatus.Model };

                // 耐用属性
                var lblDurable = new Label() { Text = "耐用属性", Left = 30, Top = 310, Width = 80 };
                var txtDurable = new TextBox() { Left = 120, Top = 308, Width = 250, Text = apparatus.DurableProperty };

                // 保存按钮
                var btnSave = new Button() { Text = "保存", Left = 220, Top = 350, Width = 80, BackColor = Color.FromArgb(0, 120, 215), ForeColor = Color.White, FlatStyle = FlatStyle.Flat };
                btnSave.FlatAppearance.BorderSize = 0;

                // 退出按钮
                var btnCancel = new Button() { Text = "退出", Left = 120, Top = 350, Width = 80, FlatStyle = FlatStyle.Flat };
                btnCancel.FlatAppearance.BorderColor = Color.Gray;
                btnCancel.FlatAppearance.BorderSize = 1;

                btnCancel.Click += (s, ev) => editForm.DialogResult = DialogResult.Cancel;

                btnSave.Click += async (s, ev) =>
                {
                    // 必填校验
                    if (string.IsNullOrWhiteSpace(txtCode.Text))
                    {
                        MessageBox.Show("器械编码不能为空！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                    if (string.IsNullOrWhiteSpace(txtName.Text))
                    {
                        MessageBox.Show("器械名称不能为空！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // 单价校验
                    if (!decimal.TryParse(txtPrice.Text, out decimal price))
                    {
                        MessageBox.Show("单价必须是有效的数字！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // 获取选中的分类ID
                    int categoryId = 0;
                    if (cmbCategory.SelectedItem is ApparatusTypeComboItem selectedType)
                    {
                        categoryId = selectedType.Id;
                    }
                    else
                    {
                        MessageBox.Show("请选择器械分类！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // 构造请求对象
                    var updateApparatus = new
                    {
                        Id = apparatus.Id,
                        ApparatusCode = txtCode.Text.Trim(),
                        ApparatusName = txtName.Text.Trim(),
                        Unit = txtUnit.Text.Trim(),
                        Price = price,
                        Manufacturer = txtManufacturer.Text.Trim(),
                        CategoryId = categoryId,
                        Model = txtModel.Text.Trim(),
                        DurableProperty = txtDurable.Text.Trim(),
                        ApparatusImage = apparatus.ApparatusImage // 保持原图片不变
                    };

                    try
                    {
                        // 检查HttpClient状态
                        if (httpClient == null)
                        {
                            MessageBox.Show("网络客户端未初始化，请重启应用程序。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }

                        // 发送更新请求
                        var json = JsonConvert.SerializeObject(updateApparatus);
                        var content = new StringContent(json, Encoding.UTF8, "application/json");
                        var response = await httpClient.PutAsync($"{API_WRITE_BASE_URL}/UpdateApparatus/UpdateApparatus", content);

                        if (response.IsSuccessStatusCode)
                        {
                            var responseContent = await response.Content.ReadAsStringAsync();

                            try
                            {
                                // 使用动态解析来处理不同的响应格式
                                var dynamicResult = JsonConvert.DeserializeObject<dynamic>(responseContent);

                                if (dynamicResult != null)
                                {
                                    // 获取code字段，可能是数字或字符串
                                    var codeValue = dynamicResult.code?.ToString();
                                    var msgValue = dynamicResult.msg?.ToString();
                                    var dataValue = dynamicResult.data;

                                    // 检查是否成功（code为200或"Success"）
                                    bool isSuccess = (codeValue == "200" || codeValue == "Success");

                                    if (isSuccess)
                                    {
                                        MessageBox.Show("器械更新成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                        editForm.DialogResult = DialogResult.OK;
                                    }
                                    else
                                    {
                                        var errorMsg = msgValue ?? "未知错误";
                                        MessageBox.Show($"更新失败: {errorMsg}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                    }
                                }
                                else
                                {
                                    MessageBox.Show("响应数据为空", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                            catch (JsonException jsonEx)
                            {
                                MessageBox.Show($"响应数据解析失败: {jsonEx.Message}\n原始响应: {responseContent}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        else
                        {
                            var errorContent = await response.Content.ReadAsStringAsync();
                            MessageBox.Show($"API请求失败: {errorContent}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                };

                // 添加控件到表单
                editForm.Controls.AddRange(new Control[] {
                    lblCategory, cmbCategory,
                    lblCodeStar, lblCode, txtCode,
                    lblNameStar, lblName, txtName,
                    lblUnit, txtUnit,
                    lblPrice, txtPrice,
                    lblManufacturer, txtManufacturer,
                    lblModel, txtModel,
                    lblDurable, txtDurable,
                    btnSave, btnCancel
                });

                // 显示表单并处理结果
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    // 编辑成功后刷新数据，保持当前分页
                    _ = Task.Run(async () => await QueryApparatusAsync());
                }
            }
        }

        /// <summary>
        /// 删除器械
        /// </summary>
        private async void DeleteApparatus(ApparatusListDto apparatus)
        {
            var result = XtraMessageBox.Show($"确定要删除器械 '{apparatus.ApparatusName}' 吗？", "确认删除", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            if (result == DialogResult.Yes)
            {
                var (success, errorMessage) = await DeleteApparatusAsync(apparatus.Id);
                if (success)
                {
                    XtraMessageBox.Show("器械删除成功。", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    // 删除成功后重新加载数据，保持当前分页
                    _ = Task.Run(async () => await QueryApparatusAsync());
                }
                else
                {
                    XtraMessageBox.Show($"删除失败: {errorMessage}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        /// <summary>
        /// 调用API删除器械
        /// </summary>
        private async Task<(bool, string)> DeleteApparatusAsync(int apparatusId)
        {
            try
            {
                // 检查HttpClient状态
                if (httpClient == null)
                {
                    return (false, "网络客户端未初始化，请重启应用程序。");
                }

                var url = $"{API_WRITE_BASE_URL}/DeleteApparatus/DeleteApparatus/{apparatusId}";
                var response = await httpClient.DeleteAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    return (true, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    var errorMessage = $"API请求失败 (状态码: {response.StatusCode})。详情: {errorContent}";
                    System.Diagnostics.Debug.WriteLine(errorMessage);
                    return (false, errorMessage);
                }
            }
            catch (Exception ex)
            {
                var innerExceptionMessage = ex.InnerException?.Message;
                var detailedMessage = string.IsNullOrEmpty(innerExceptionMessage) ? ex.Message : $"{ex.Message} (内部错误: {innerExceptionMessage})";
                var errorMessage = $"网络或连接异常: {detailedMessage}";
                System.Diagnostics.Debug.WriteLine(errorMessage);
                return (false, errorMessage);
            }
        }

        /// <summary>
        /// 加载器械分类下拉框数据
        /// </summary>
        /// <param name="forceRefresh">是否强制刷新</param>
        private async Task LoadApparatusTypeComboAsync(bool forceRefresh = false)
        {
            try
            {
                // 检查HttpClient状态
                if (httpClient == null)
                {
                    XtraMessageBox.Show("网络客户端未初始化，请重启应用程序。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 清空现有项
                comboApparatusType.Properties.Items.Clear();
                comboApparatusType.Properties.Items.Add(new ApparatusTypeComboItem
                {
                    Id = 0,
                    Name = "所有"
                });

                // 构建API请求URL - 如果是强制刷新，则获取所有状态的分类，否则只获取启用的分类
                string apiUrl = $"{API_BASE_URL}/GetApparatusTypeTree{(forceRefresh ? "" : "?Status=1")}";

                // 发送GET请求获取器械分类树
                using (var response = await httpClient.GetAsync(apiUrl))
                {
                    if (response.IsSuccessStatusCode)
                    {
                        var jsonString = await response.Content.ReadAsStringAsync();
                        // 尝试解析API响应
                        try
                        {
                            // 首先尝试使用标准ApiResponse格式解析
                            var apiResponse = JsonConvert.DeserializeObject<ApiResponseModel>(jsonString);

                            // 检查是否成功解析并且code为200
                            if (apiResponse != null && apiResponse.code == 200 && apiResponse.data != null)
                            {
                                // 解析器械分类树数据
                                var apparatusTypes = apiResponse.data.ToObject<List<ApparatusTypeComboItem>>();
                                if (apparatusTypes != null && apparatusTypes.Count > 0)
                                {
                                    // 添加器械分类到下拉框
                                    foreach (var type in apparatusTypes)
                                    {
                                        AddApparatusTypeToComboBox(type, "");
                                    }
                                }
                            }
                            else
                            {
                                // 尝试使用替代格式解析
                                // 检查是否是格式为 {"msg":"获取成功","data":[...]} 的响应
                                var alternateResponse = JsonConvert.DeserializeObject<dynamic>(jsonString);
                                if (alternateResponse != null && alternateResponse.data != null)
                                {
                                    try
                                    {
                                        // 解析器械分类树数据
                                        var apparatusTypes = ((JToken)alternateResponse.data).ToObject<List<ApparatusTypeComboItem>>();
                                        if (apparatusTypes != null && apparatusTypes.Count > 0)
                                        {
                                            // 添加器械分类到下拉框
                                            foreach (var type in apparatusTypes)
                                            {
                                                AddApparatusTypeToComboBox(type, "");
                                            }
                                            return; // 成功处理，提前返回
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"尝试替代格式解析失败: {ex.Message}");
                                    }
                                }

                                // 如果所有尝试都失败，显示错误消息
                                XtraMessageBox.Show($"获取器械分类失败：{(apiResponse?.msg ?? "未知错误")}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        catch (Exception parseEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"API响应解析异常: {parseEx.Message}");
                            XtraMessageBox.Show($"解析API响应时出错: {parseEx.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        XtraMessageBox.Show($"获取器械分类失败：{response.ReasonPhrase}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }

                // 默认选择第一项
                if (comboApparatusType.Properties.Items.Count > 0)
                {
                    comboApparatusType.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"加载器械分类出错：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        /// <summary>
        /// 递归添加器械分类到下拉框
        /// </summary>
        private void AddApparatusTypeToComboBox(ApparatusTypeComboItem type, string prefix)
        {
            // 添加当前分类
            comboApparatusType.Properties.Items.Add(new ApparatusTypeComboItem
            {
                Id = type.Id,
                Name = $"{prefix}{type.Name}"
            });
        }

        /// <summary>
        /// 查询器械列表
        /// </summary>
        private async Task QueryApparatusAsync()
        {
            try
            {
                // 检查HttpClient状态
                if (httpClient == null)
                {
                    XtraMessageBox.Show("网络客户端未初始化，请重启应用程序。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 构建查询参数
                var queryParams = new Dictionary<string, string>
                {
                    { "PageIndex", _currentPageIndex.ToString() },
                    { "PageSize", _pageSize.ToString() }
                };

                // 添加器械名称查询条件（如果有）
                // 这里可以添加更多查询条件

                // 添加状态查询条件
                if (!chkShowDisabled.Checked)
                {
                    queryParams.Add("Status", "1"); // 只显示启用的器械
                }

                // 添加分类查询条件
                if (comboApparatusType.SelectedItem is ApparatusTypeComboItem selectedType && selectedType.Id > 0)
                {
                    queryParams.Add("CategoryId", selectedType.Id.ToString());
                }

                // 构建查询字符串
                var queryString = string.Join("&", queryParams.Select(p => $"{p.Key}={Uri.EscapeDataString(p.Value)}"));

                // 构建API请求URL
                string apiUrl = $"{API_BASE_URL}/QueryApparatus?{queryString}";

                // 发送GET请求查询器械列表
                using (var response = await httpClient.GetAsync(apiUrl))
                {
                    if (response.IsSuccessStatusCode)
                    {
                        var jsonString = await response.Content.ReadAsStringAsync();

                        try
                        {
                            // 首先尝试使用标准ApiResponse格式解析
                            var apiResponse = JsonConvert.DeserializeObject<ApiResponseModel>(jsonString);

                            // 检查是否成功解析并且code为200
                            if (apiResponse != null && apiResponse.code == 200 && apiResponse.data != null)
                            {
                                try
                                {
                                    // 解析分页数据
                                    var pageData = apiResponse.data.ToObject<PageDataModel<ApparatusListDto>>();
                                    if (pageData != null)
                                    {
                                        // 设置状态显示名称
                                        foreach (var item in pageData.Items)
                                        {
                                            item.StatusName = item.Status == 1 ? "启用" : "停用";
                                        }

                                        // 更新分页信息
                                        _totalCount = pageData.TotalCount;
                                        _totalPages = (int)Math.Ceiling((double)_totalCount / _pageSize);

                                        // 更新分页控件状态
                                        UpdatePaginationControls();

                                        // 绑定数据到表格
                                        gridApparatus.DataSource = pageData.Items;
                                        gridViewApparatus.RefreshData();
                                    }
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"解析分页数据出错：{ex.Message}");
                                    XtraMessageBox.Show($"解析分页数据出错：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                            else
                            {
                                // 尝试使用替代格式解析
                                // 检查是否是格式为 {"msg":"获取成功","data":{...}} 的响应
                                var alternateResponse = JsonConvert.DeserializeObject<dynamic>(jsonString);
                                if (alternateResponse != null && alternateResponse.data != null)
                                {
                                    try
                                    {
                                        // 解析分页数据
                                        var pageData = ((JToken)alternateResponse.data).ToObject<PageDataModel<ApparatusListDto>>();
                                        if (pageData != null)
                                        {
                                            // 设置状态显示名称
                                            foreach (var item in pageData.Items)
                                            {
                                                item.StatusName = item.Status == 1 ? "启用" : "停用";
                                            }

                                            // 更新分页信息
                                            _totalCount = pageData.TotalCount;
                                            _totalPages = (int)Math.Ceiling((double)_totalCount / _pageSize);

                                            // 更新分页控件状态
                                            UpdatePaginationControls();

                                            // 绑定数据到表格
                                            gridApparatus.DataSource = pageData.Items;
                                            gridViewApparatus.RefreshData();
                                            return; // 成功处理，提前返回
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"尝试替代格式解析失败: {ex.Message}");
                                    }
                                }

                                // 如果所有尝试都失败，显示错误消息
                                XtraMessageBox.Show($"查询器械列表失败：{(apiResponse?.msg ?? "未知错误")}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        catch (Exception parseEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"API响应解析异常: {parseEx.Message}");
                            XtraMessageBox.Show($"解析API响应时出错: {parseEx.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        XtraMessageBox.Show($"查询器械列表失败：{response.ReasonPhrase}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"查询器械列表出错：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 更新分页控件状态
        /// </summary>
        private void UpdatePaginationControls()
        {
            try
            {
                // 确保在UI线程上执行
                if (this.InvokeRequired)
                {
                    this.Invoke((MethodInvoker)delegate { UpdatePaginationControls(); });
                    return;
                }

                // 更新页码信息
                lblPageInfo.Text = $"第{_currentPageIndex}页/共{_totalPages}页";

                // 更新合计标签
                lblTotal.Text = $"合计：{_totalCount}条";

                // 更新按钮状态
                btnPrevPage.Enabled = _currentPageIndex > 1;
                btnNextPage.Enabled = _currentPageIndex < _totalPages;

                // 如果没有数据，禁用所有分页按钮
                if (_totalCount == 0)
                {
                    btnPrevPage.Enabled = false;
                    btnNextPage.Enabled = false;
                    lblPageInfo.Text = "第0页/共0页";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新分页控件状态时出错：{ex.Message}");
            }
        }
    }



    /// <summary>
    /// 器械列表DTO
    /// </summary>
    public class ApparatusListDto
    {
        public int Id { get; set; }
        public string ApparatusImage { get; set; }
        public string ApparatusCode { get; set; }
        public string ApparatusName { get; set; }
        public string Unit { get; set; }
        public decimal Price { get; set; }
        public string Manufacturer { get; set; }
        public int Status { get; set; }
        public string StatusName { get; set; }
        public int CategoryId { get; set; }
        public string CategoryName { get; set; }
        public string Model { get; set; }
        public string DurableProperty { get; set; }
    }



    /// <summary>
    /// 器械分类下拉框项
    /// </summary>
    public class ApparatusTypeComboItem
    {
        public int Id { get; set; }
        public string Name { get; set; }

        public override string ToString()
        {
            return Name;
        }
    }


}