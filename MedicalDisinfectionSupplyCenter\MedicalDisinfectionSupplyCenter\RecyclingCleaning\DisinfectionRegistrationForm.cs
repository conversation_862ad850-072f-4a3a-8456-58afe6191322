using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using WinFormsAppDemo2.Common;
using System.Net.Http;

namespace MedicalDisinfectionSupplyCenter.RecyclingCleaning
{
    // 已清洗物品信息数据模型（对应API返回的数据结构）
    public class DisinfectionCleanedItemInfo
    {
        public int id { get; set; }
        public string itemName { get; set; }
        public string itemCode { get; set; }
        public string department { get; set; }
        public string cleaningTime { get; set; }
        public string useDepartment { get; set; }
        public string cleaningBatch { get; set; }
        public string cleaner { get; set; }
    }

    // 消毒登记请求数据模型
    public class DisinfectionRegistrationRequest
    {
        public int equipmentId { get; set; }
        public string @operator { get; set; }
        public string startTime { get; set; }
        public string disinfectionMode { get; set; }
        public int disinfectionTemperature { get; set; }
        public List<int> itemIds { get; set; }
    }

    public partial class DisinfectionRegistrationForm : Form
    {
        // API配置
        private readonly string API_BASE_URL = ReadApiConfig.BaseUrl.TrimEnd('/');
        private readonly string API_BASE_URLWrite = WriteApiConfig.BaseUrl.TrimEnd('/');

        public string Disinfector { get; private set; }
        public DateTime StartTime { get; private set; }
        public string DisinfectionMethod { get; private set; }
        public string DisinfectionEquipment { get; private set; }
        public string DisinfectionTemperature { get; private set; }
        public List<DisinfectionItem> SelectedItems { get; private set; }

        private TextBox txtDisinfector;
        private DateTimePicker dtpStartTime;
        private System.Windows.Forms.ComboBox cmbDisinfectionMethod;
        private System.Windows.Forms.ComboBox cmbDisinfectionEquipment;
        private TextBox txtDisinfectionTemperature;
        private GridControl gridControl1;
        private GridView gridView1;
        private Button btnOK;
        private Button btnCancel;
        private Label lblDisinfector;
        private Label lblStartTime;
        private Label lblDisinfectionMethod;
        private Label lblDisinfectionEquipment;
        private Label lblDisinfectionTemperature;
        private Label lblTitle;

        public class DisinfectionItem
        {
            public int Id { get; set; }
            public bool Selected { get; set; }
            public string ItemName { get; set; }
            public string ItemCode { get; set; }
            public string Department { get; set; }
            public DateTime CleaningTime { get; set; }
        }

        public DisinfectionRegistrationForm()
        {
            InitializeComponent();

            // 设置默认值
            dtpStartTime.Value = DateTime.Now;
            txtDisinfector.Text = Environment.UserName; // 默认当前用户

            // 初始化选中项目列表
            SelectedItems = new List<DisinfectionItem>();

            // 初始化网格
            InitializeGrid();

            // 异步加载API数据
            LoadDataFromApiAsync();
        }

        private void InitializeComponent()
        {
            this.lblTitle = new System.Windows.Forms.Label();
            this.lblDisinfector = new System.Windows.Forms.Label();
            this.txtDisinfector = new System.Windows.Forms.TextBox();
            this.lblStartTime = new System.Windows.Forms.Label();
            this.dtpStartTime = new System.Windows.Forms.DateTimePicker();
            this.lblDisinfectionMethod = new System.Windows.Forms.Label();
            this.cmbDisinfectionMethod = new System.Windows.Forms.ComboBox();
            this.lblDisinfectionEquipment = new System.Windows.Forms.Label();
            this.cmbDisinfectionEquipment = new System.Windows.Forms.ComboBox();
            this.lblDisinfectionTemperature = new System.Windows.Forms.Label();
            this.txtDisinfectionTemperature = new System.Windows.Forms.TextBox();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.btnOK = new System.Windows.Forms.Button();
            this.btnCancel = new System.Windows.Forms.Button();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            this.SuspendLayout();
            //
            // lblTitle
            //
            this.lblTitle.AutoSize = true;
            this.lblTitle.Font = new System.Drawing.Font("微软雅黑", 14F, System.Drawing.FontStyle.Bold);
            this.lblTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(123)))), ((int)(((byte)(255)))));
            this.lblTitle.Location = new System.Drawing.Point(30, 20);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(134, 37);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "消毒登记";
            //
            // lblDisinfector
            //
            this.lblDisinfector.AutoSize = true;
            this.lblDisinfector.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblDisinfector.Location = new System.Drawing.Point(30, 70);
            this.lblDisinfector.Name = "lblDisinfector";
            this.lblDisinfector.Size = new System.Drawing.Size(74, 27);
            this.lblDisinfector.TabIndex = 1;
            this.lblDisinfector.Text = "消毒人";
            //
            // txtDisinfector
            //
            this.txtDisinfector.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.txtDisinfector.Location = new System.Drawing.Point(110, 67);
            this.txtDisinfector.Name = "txtDisinfector";
            this.txtDisinfector.Size = new System.Drawing.Size(150, 34);
            this.txtDisinfector.TabIndex = 2;
            //
            // lblStartTime
            //
            this.lblStartTime.AutoSize = true;
            this.lblStartTime.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblStartTime.Location = new System.Drawing.Point(30, 110);
            this.lblStartTime.Name = "lblStartTime";
            this.lblStartTime.Size = new System.Drawing.Size(100, 27);
            this.lblStartTime.TabIndex = 3;
            this.lblStartTime.Text = "开始时间";
            //
            // dtpStartTime
            //
            this.dtpStartTime.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            this.dtpStartTime.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.dtpStartTime.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.dtpStartTime.Location = new System.Drawing.Point(140, 107);
            this.dtpStartTime.Name = "dtpStartTime";
            this.dtpStartTime.Size = new System.Drawing.Size(200, 34);
            this.dtpStartTime.TabIndex = 4;
            //
            // lblDisinfectionMethod
            //
            this.lblDisinfectionMethod.AutoSize = true;
            this.lblDisinfectionMethod.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblDisinfectionMethod.Location = new System.Drawing.Point(30, 150);
            this.lblDisinfectionMethod.Name = "lblDisinfectionMethod";
            this.lblDisinfectionMethod.Size = new System.Drawing.Size(100, 27);
            this.lblDisinfectionMethod.TabIndex = 5;
            this.lblDisinfectionMethod.Text = "消毒方式";
            //
            // cmbDisinfectionMethod
            //
            this.cmbDisinfectionMethod.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbDisinfectionMethod.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.cmbDisinfectionMethod.FormattingEnabled = true;
            this.cmbDisinfectionMethod.Items.AddRange(new object[] {
            "高温",
            "化学消毒",
            "紫外线消毒",
            "臭氧消毒",
            "等离子消毒"});
            this.cmbDisinfectionMethod.Location = new System.Drawing.Point(140, 147);
            this.cmbDisinfectionMethod.Name = "cmbDisinfectionMethod";
            this.cmbDisinfectionMethod.Size = new System.Drawing.Size(120, 35);
            this.cmbDisinfectionMethod.TabIndex = 6;
            //
            // lblDisinfectionEquipment
            //
            this.lblDisinfectionEquipment.AutoSize = true;
            this.lblDisinfectionEquipment.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblDisinfectionEquipment.Location = new System.Drawing.Point(30, 190);
            this.lblDisinfectionEquipment.Name = "lblDisinfectionEquipment";
            this.lblDisinfectionEquipment.Size = new System.Drawing.Size(100, 27);
            this.lblDisinfectionEquipment.TabIndex = 7;
            this.lblDisinfectionEquipment.Text = "消毒设备";
            //
            // cmbDisinfectionEquipment
            //
            this.cmbDisinfectionEquipment.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbDisinfectionEquipment.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.cmbDisinfectionEquipment.FormattingEnabled = true;
            this.cmbDisinfectionEquipment.Items.AddRange(new object[] {
            "设备1",
            "设备2",
            "设备3"});
            this.cmbDisinfectionEquipment.Location = new System.Drawing.Point(140, 187);
            this.cmbDisinfectionEquipment.Name = "cmbDisinfectionEquipment";
            this.cmbDisinfectionEquipment.Size = new System.Drawing.Size(120, 35);
            this.cmbDisinfectionEquipment.TabIndex = 8;
            //
            // lblDisinfectionTemperature
            //
            this.lblDisinfectionTemperature.AutoSize = true;
            this.lblDisinfectionTemperature.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.lblDisinfectionTemperature.Location = new System.Drawing.Point(30, 230);
            this.lblDisinfectionTemperature.Name = "lblDisinfectionTemperature";
            this.lblDisinfectionTemperature.Size = new System.Drawing.Size(100, 27);
            this.lblDisinfectionTemperature.TabIndex = 9;
            this.lblDisinfectionTemperature.Text = "消毒温度";
            //
            // txtDisinfectionTemperature
            //
            this.txtDisinfectionTemperature.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.txtDisinfectionTemperature.Location = new System.Drawing.Point(140, 227);
            this.txtDisinfectionTemperature.Name = "txtDisinfectionTemperature";
            this.txtDisinfectionTemperature.Size = new System.Drawing.Size(120, 34);
            this.txtDisinfectionTemperature.TabIndex = 10;
            //
            // gridControl1
            //
            this.gridControl1.Location = new System.Drawing.Point(300, 67);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(550, 350);
            this.gridControl1.TabIndex = 11;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            //
            // gridView1
            //
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            //
            // btnOK
            //
            this.btnOK.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(123)))), ((int)(((byte)(255)))));
            this.btnOK.FlatAppearance.BorderSize = 0;
            this.btnOK.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnOK.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Bold);
            this.btnOK.ForeColor = System.Drawing.Color.White;
            this.btnOK.Location = new System.Drawing.Point(650, 440);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new System.Drawing.Size(90, 40);
            this.btnOK.TabIndex = 12;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = false;
            this.btnOK.Click += new System.EventHandler(this.BtnOK_Click);
            //
            // btnCancel
            //
            this.btnCancel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(117)))), ((int)(((byte)(125)))));
            this.btnCancel.FlatAppearance.BorderSize = 0;
            this.btnCancel.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCancel.Font = new System.Drawing.Font("微软雅黑", 10F, System.Drawing.FontStyle.Bold);
            this.btnCancel.ForeColor = System.Drawing.Color.White;
            this.btnCancel.Location = new System.Drawing.Point(760, 440);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(90, 40);
            this.btnCancel.TabIndex = 13;
            this.btnCancel.Text = "退出";
            this.btnCancel.UseVisualStyleBackColor = false;
            this.btnCancel.Click += new System.EventHandler(this.BtnCancel_Click);
            //
            // DisinfectionRegistrationForm
            //
            this.AcceptButton = this.btnOK;
            this.AutoScaleDimensions = new System.Drawing.SizeF(9F, 18F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new System.Drawing.Size(880, 500);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnOK);
            this.Controls.Add(this.gridControl1);
            this.Controls.Add(this.txtDisinfectionTemperature);
            this.Controls.Add(this.lblDisinfectionTemperature);
            this.Controls.Add(this.cmbDisinfectionEquipment);
            this.Controls.Add(this.lblDisinfectionEquipment);
            this.Controls.Add(this.cmbDisinfectionMethod);
            this.Controls.Add(this.lblDisinfectionMethod);
            this.Controls.Add(this.dtpStartTime);
            this.Controls.Add(this.lblStartTime);
            this.Controls.Add(this.txtDisinfector);
            this.Controls.Add(this.lblDisinfector);
            this.Controls.Add(this.lblTitle);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "DisinfectionRegistrationForm";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "消毒登记";
            this.Load += new System.EventHandler(this.DisinfectionRegistrationForm_Load);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private void InitializeGrid()
        {
            // 设置网格属性
            gridView1.OptionsBehavior.Editable = false;
            gridView1.OptionsView.ShowGroupPanel = false;
            gridView1.OptionsView.ShowIndicator = true;
            gridView1.OptionsSelection.MultiSelect = true;
            gridView1.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CheckBoxRowSelect;
            gridView1.OptionsSelection.ShowCheckBoxSelectorInColumnHeader = DevExpress.Utils.DefaultBoolean.True;

            // 设置列
            gridView1.Columns.Clear();
            var colSelected = gridView1.Columns.AddField("Selected");
            colSelected.Visible = false; // 隐藏，因为使用CheckBox选择模式

            var colItemName = gridView1.Columns.AddVisible("ItemName", "物品名称");
            colItemName.Width = 120;

            var colItemCode = gridView1.Columns.AddVisible("ItemCode", "物品条码");
            colItemCode.Width = 100;

            var colDepartment = gridView1.Columns.AddVisible("Department", "使用科室");
            colDepartment.Width = 100;

            var colCleaningTime = gridView1.Columns.AddVisible("CleaningTime", "清洗时间");
            colCleaningTime.Width = 150;
            colCleaningTime.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            colCleaningTime.DisplayFormat.FormatString = "yyyy-MM-dd HH:mm:ss";

            // 设置行高
            gridView1.RowHeight = 30;
        }

        /// <summary>
        /// 异步从API加载已清洗的物品数据
        /// </summary>
        private async void LoadDataFromApiAsync()
        {
            try
            {
                // 显示加载提示
                var loadingData = new List<DisinfectionItem>
                {
                    new DisinfectionItem { Id = 0, Selected = false, ItemName = "正在加载数据...", ItemCode = "", Department = "", CleaningTime = DateTime.Now }
                };
                gridControl1.DataSource = loadingData;

                // 构建API URL
                string apiUrl = $"{API_BASE_URL}/RecyclingCleaning/GetCleanedItemList";


                // 调用API
                string jsonResult = await HttpClientHelper.ClientAsync("GET", apiUrl, false, null);

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    System.Diagnostics.Debug.WriteLine($"API返回数据: {jsonResult.Substring(0, Math.Min(200, jsonResult.Length))}...");

                    // 解析API响应 - 使用CleaningRegistrationForm中已定义的ApiResponse
                    var apiResponse = JsonConvert.DeserializeObject<ApiResponse<List<DisinfectionCleanedItemInfo>>>(jsonResult);

                    if (apiResponse != null && apiResponse.data != null)
                    {
                        // 转换API数据为DisinfectionItem格式
                        var disinfectionItems = new List<DisinfectionItem>();

                        foreach (var item in apiResponse.data)
                        {
                            var disinfectionItem = new DisinfectionItem
                            {
                                Id = item.id,
                                Selected = false,
                                ItemName = item.itemName ?? "未知物品",
                                ItemCode = item.itemCode ?? "",
                                Department = item.useDepartment ?? item.department ?? "未知科室",
                                CleaningTime = DateTime.TryParse(item.cleaningTime, out DateTime cleanTime) ? cleanTime : DateTime.Now
                            };
                            disinfectionItems.Add(disinfectionItem);
                        }

                        // 更新网格数据
                        gridControl1.DataSource = disinfectionItems;

                        System.Diagnostics.Debug.WriteLine($"成功加载 {disinfectionItems.Count} 条已清洗物品数据");
                    }
                    else
                    {
                        string errorMsg = apiResponse?.msg ?? "API返回格式错误";
                        System.Diagnostics.Debug.WriteLine($"API调用失败: {errorMsg}");
                        LoadFallbackData($"API调用失败: {errorMsg}");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("API返回空数据");
                    LoadFallbackData("API返回空数据");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载数据异常: {ex.Message}");
                LoadFallbackData($"加载数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载备用数据（当API调用失败时）
        /// </summary>
        private void LoadFallbackData(string errorMessage)
        {
            var fallbackData = new List<DisinfectionItem>
            {
                new DisinfectionItem { Id = 1, Selected = false, ItemName = "骨科手术刀", ItemCode = "1235", Department = "内科", CleaningTime = DateTime.Parse("2022-07-22 10:10:12") },
                new DisinfectionItem { Id = 2, Selected = false, ItemName = "骨科小手术刀", ItemCode = "1231", Department = "内科", CleaningTime = DateTime.Parse("2022-07-22 10:10:12") },
                new DisinfectionItem { Id = 3, Selected = false, ItemName = "手术钳", ItemCode = "1236", Department = "外科", CleaningTime = DateTime.Parse("2022-07-22 09:30:15") },
                new DisinfectionItem { Id = 4, Selected = false, ItemName = "止血钳", ItemCode = "1237", Department = "外科", CleaningTime = DateTime.Parse("2022-07-22 09:45:20") },
                new DisinfectionItem { Id = 5, Selected = false, ItemName = "缝合针", ItemCode = "1238", Department = "外科", CleaningTime = DateTime.Parse("2022-07-22 10:00:30") }
            };

            gridControl1.DataSource = fallbackData;

            // 显示错误信息（可选）
            System.Diagnostics.Debug.WriteLine($"使用备用数据，原因: {errorMessage}");
        }

        /// <summary>
        /// 刷新数据 - 重新从API加载数据
        /// </summary>
        public async Task RefreshDataAsync()
        {
            LoadDataFromApiAsync();
        }

        /// <summary>
        /// 测试API连接
        /// </summary>
        public async Task<bool> TestApiConnectionAsync()
        {
            try
            {
                string apiUrl = $"{API_BASE_URL}RecyclingCleaning/GetCleanedItemList";
                string result = await HttpClientHelper.ClientAsync("GET", apiUrl, false, null);
                return !string.IsNullOrEmpty(result);
            }
            catch
            {
                return false;
            }
        }

        private void DisinfectionRegistrationForm_Load(object sender, EventArgs e)
        {
            // 设置默认选择
            cmbDisinfectionMethod.SelectedIndex = 0; // 默认选择第一个消毒方式
            cmbDisinfectionEquipment.SelectedIndex = 0; // 默认选择第一个设备
        }

        private async void BtnOK_Click(object sender, EventArgs e)
        {
            // 验证输入
            if (string.IsNullOrWhiteSpace(txtDisinfector.Text))
            {
                MessageBox.Show("请输入消毒人员", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDisinfector.Focus();
                return;
            }

            if (cmbDisinfectionMethod.SelectedIndex == -1)
            {
                MessageBox.Show("请选择消毒方式", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbDisinfectionMethod.Focus();
                return;
            }

            if (cmbDisinfectionEquipment.SelectedIndex == -1)
            {
                MessageBox.Show("请选择消毒设备", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbDisinfectionEquipment.Focus();
                return;
            }

            // 获取选中的物品
            var selectedRows = gridView1.GetSelectedRows();
            if (selectedRows.Length == 0)
            {
                MessageBox.Show("请至少选择一个物品进行消毒", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 禁用按钮防止重复提交
            btnOK.Enabled = false;
            btnOK.Text = "提交中...";

            try
            {
                // 调用API提交消毒登记
                bool success = await SubmitDisinfectionRegistrationAsync(selectedRows);

                if (success)
                {
                    // 保存数据到属性（用于返回给调用方）
                    Disinfector = txtDisinfector.Text.Trim();
                    StartTime = dtpStartTime.Value;
                    DisinfectionMethod = cmbDisinfectionMethod.SelectedItem.ToString();
                    DisinfectionEquipment = cmbDisinfectionEquipment.SelectedItem.ToString();
                    DisinfectionTemperature = txtDisinfectionTemperature.Text.Trim();

                    // 获取选中的物品列表
                    SelectedItems = new List<DisinfectionItem>();
                    foreach (int rowHandle in selectedRows)
                    {
                        if (rowHandle >= 0)
                        {
                            var item = gridView1.GetRow(rowHandle) as DisinfectionItem;
                            if (item != null)
                            {
                                item.Selected = true;
                                SelectedItems.Add(item);
                            }
                        }
                    }

                    MessageBox.Show("消毒登记提交成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"提交消毒登记失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 恢复按钮状态
                btnOK.Enabled = true;
                btnOK.Text = "确定";
            }
        }

        /// <summary>
        /// 异步提交消毒登记到API
        /// </summary>
        /// <param name="selectedRows">选中的行</param>
        /// <returns>提交是否成功</returns>
        private async Task<bool> SubmitDisinfectionRegistrationAsync(int[] selectedRows)
        {
            try
            {
                // 获取选中物品的ID列表
                var itemIds = new List<int>();
                foreach (int rowHandle in selectedRows)
                {
                    if (rowHandle >= 0)
                    {
                        var item = gridView1.GetRow(rowHandle) as DisinfectionItem;
                        if (item != null)
                        {
                            itemIds.Add(item.Id);
                        }
                    }
                }

                // 解析消毒温度
                int temperature = 0;
                if (!string.IsNullOrWhiteSpace(txtDisinfectionTemperature.Text))
                {
                    int.TryParse(txtDisinfectionTemperature.Text.Trim(), out temperature);
                }

                // 构建API请求数据
                var requestData = new DisinfectionRegistrationRequest
                {
                    equipmentId = cmbDisinfectionEquipment.SelectedIndex + 1, // 假设设备ID从1开始
                    @operator = txtDisinfector.Text.Trim(),
                    startTime = dtpStartTime.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                    disinfectionMode = cmbDisinfectionMethod.SelectedItem.ToString(),
                    disinfectionTemperature = temperature,
                    itemIds = itemIds
                };

                // 序列化请求数据
                string jsonData = JsonConvert.SerializeObject(requestData);
                var content = new StringContent(jsonData, System.Text.Encoding.UTF8, "application/json");

                // 构建API URL
                string apiUrl = $"{API_BASE_URLWrite}/RecyclingCleaning/AddDisinfection";

                System.Diagnostics.Debug.WriteLine($"正在调用消毒登记API: {apiUrl}");
                System.Diagnostics.Debug.WriteLine($"请求数据: {jsonData}");

                // 调用API
                string jsonResult = await HttpClientHelper.ClientAsync("POST", apiUrl, false, content);

                if (!string.IsNullOrEmpty(jsonResult))
                {
                    System.Diagnostics.Debug.WriteLine($"API返回数据: {jsonResult}");

                    return true;

                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("消毒登记API返回空数据");
                    MessageBox.Show("API返回空数据", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"提交消毒登记异常: {ex.Message}");
                MessageBox.Show($"提交异常: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
